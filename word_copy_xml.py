"""
SOTA级别的Word文档章节复制工具 - XML操作版本
100%保持原始格式，直接操作DOCX的XML结构

特性:
- 直接操作document.xml实现精确复制
- 完整保持所有XML属性和命名空间
- 自动处理styles.xml样式定义同步
- 智能处理图片、表格等复杂元素
- 维护relationships和media资源完整性
- 支持复杂文档结构和交叉引用
"""

import re
import os
import copy
import zipfile
import shutil
import logging
from pathlib import Path
from typing import List, Optional, Union, Dict, Any, Set, Tuple
from dataclasses import dataclass, field
from functools import wraps

try:
    from lxml import etree
    from lxml.etree import Element
except ImportError as e:
    raise ImportError(f"lxml is required: {e}. Please install: pip install lxml")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def handle_exceptions(default_return=None):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                if default_return is not None:
                    return default_return
                raise
        return wrapper
    return decorator


class WordCopyError(Exception):
    """Word文档复制异常基类"""
    pass


class Config:
    """配置类，集中管理所有配置常量"""
    
    # Word XML命名空间定义
    NAMESPACES = {
        'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
        'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
        'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
        'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
        'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture',
        'v': 'urn:schemas-microsoft-com:vml',
        'o': 'urn:schemas-microsoft-com:office:office',
        'w10': 'urn:schemas-microsoft-com:office:word',
        'm': 'http://schemas.openxmlformats.org/officeDocument/2006/math',
        'wp14': 'http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing',
        'wps': 'http://schemas.microsoft.com/office/word/2010/wordprocessingShape',
        'wpg': 'http://schemas.microsoft.com/office/word/2010/wordprocessingGroup',
        'mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
        'cp': 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties',
        'dc': 'http://purl.org/dc/elements/1.1/',
        'dcterms': 'http://purl.org/dc/terms/',
        'dcmitype': 'http://purl.org/dc/dcmitype/',
        'xsi': 'http://www.w3.org/2001/XMLSchema-instance',
        'rel': 'http://schemas.openxmlformats.org/package/2006/relationships'
    }
    
    # 标题样式映射配置
    HEADING_STYLES_MAP = {
        1: ['2', 'Heading1', 'Heading 1', '标题 1', '标题1'],
        2: ['3', 'Heading2', 'Heading 2', '标题 2', '标题2'], 
        3: ['4', 'Heading3', 'Heading 3', '标题 3', '标题3'],
        4: ['5', 'Heading4', 'Heading 4', '标题 4', '标题4'],
        5: ['6', 'Heading5', 'Heading 5', '标题 5', '标题5'],
        6: ['7', 'Heading6', 'Heading 6', '标题 6', '标题6'],
        7: ['8', 'Heading7', 'Heading 7', '标题 7', '标题7'],
        8: ['9', 'Heading8', 'Heading 8', '标题 8', '标题8'],
        9: ['10', 'Heading9', 'Heading 9', '标题 9', '标题9'],
    }
    
    # 媒体查询模式
    MEDIA_QUERY_PATTERNS = [
        './/a:blip[@r:embed]',  # DrawingML图片
        './/v:imagedata[@r:id]',  # VML图片 
        './/pic:pic//a:blip[@r:embed]',  # Picture元素中的图片
        './/w:object//o:OLEObject[@r:id]',  # OLE对象
        './/w:pict//v:imagedata[@r:id]',  # 图片元素
        './/*[@r:embed]',  # 任何有embed属性的元素
        './/*[@r:id]'  # 任何有关系ID的元素
    ]
    
    # 必需的DOCX文件
    REQUIRED_DOCX_FILES = [
        'word/document.xml',
        '[Content_Types].xml',
        '_rels/.rels'
    ]
    
    # 格式文件配置
    FORMAT_FILES_CONFIG = {
        'word/styles.xml': 'styles_xml',
        'word/numbering.xml': 'numbering_xml',
        'word/fontTable.xml': 'font_table_xml', 
        'word/theme/theme1.xml': 'theme_xml',
        'word/settings.xml': 'settings_xml'
    }



@dataclass
class DocumentStructure:
    """文档结构信息"""
    document_xml: bytes
    styles_xml: Optional[bytes] = None
    numbering_xml: Optional[bytes] = None  # 编号/列表样式
    font_table_xml: Optional[bytes] = None  # 字体表
    theme_xml: Optional[bytes] = None       # 主题文件
    settings_xml: Optional[bytes] = None    # 文档设置
    relationships: Dict[str, bytes] = field(default_factory=dict)
    media_files: Dict[str, bytes] = field(default_factory=dict)
    other_files: Dict[str, bytes] = field(default_factory=dict)
    content_types: Optional[bytes] = None
    
    
@dataclass
class SectionInfo:
    """章节信息"""
    start_element: Element
    end_element: Optional[Element]
    xml_elements: List[Element]
    title: str
    level: int
    used_styles: Set[str] = field(default_factory=set)
    used_relationships: Set[str] = field(default_factory=set)
    media_references: Set[str] = field(default_factory=set)
    numbering_references: Set[str] = field(default_factory=set)  # 编号引用


class XMLDocumentProcessor:
    """XML文档处理器 - 核心引擎"""
    
    def __init__(self):
        self.namespaces = Config.NAMESPACES.copy()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._register_all_namespaces()
    
    def _register_all_namespaces(self) -> None:
        """注册所有XML命名空间"""
        for prefix, uri in self.namespaces.items():
            etree.register_namespace(prefix, uri)
    
    @handle_exceptions()
    def extract_document_structure(self, docx_path: Union[str, Path]) -> DocumentStructure:
        """提取文档完整结构"""
        docx_path = Path(docx_path)
        
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            # 提取核心文档XML
            try:
                document_xml = docx_zip.read('word/document.xml')
            except KeyError:
                raise WordCopyError("Invalid DOCX file: missing word/document.xml")
            
            # 创建结构对象，必须提供document_xml参数
            structure = DocumentStructure(document_xml=document_xml)
            
            # 提取格式相关文件
            format_files = Config.FORMAT_FILES_CONFIG
            
            for file_path, attr_name in format_files.items():
                try:
                    file_data = docx_zip.read(file_path)
                    setattr(structure, attr_name, file_data)
                except KeyError:
                    setattr(structure, attr_name, None)
            
            # 提取内容类型定义
            try:
                structure.content_types = docx_zip.read('[Content_Types].xml')
            except KeyError:
                pass
            
            # 提取关系文件
            for file_path in docx_zip.namelist():
                if file_path.endswith('.rels'):
                    structure.relationships[file_path] = docx_zip.read(file_path)
                elif file_path.startswith('word/media/'):
                    structure.media_files[file_path] = docx_zip.read(file_path)
                elif not file_path.endswith('/'):  # 排除目录
                    structure.other_files[file_path] = docx_zip.read(file_path)
        
        return structure
    
    @handle_exceptions()
    def parse_document_xml(self, xml_data: bytes) -> Element:
        """解析文档XML"""
        try:
            # 使用兼容的XMLParser参数
            parser = etree.XMLParser(recover=True, remove_blank_text=False)
            root = etree.fromstring(xml_data, parser)
            return root
        except etree.XMLSyntaxError as e:
            raise WordCopyError(f"Invalid XML structure: {e}")
    
    @handle_exceptions(default_return=None)
    def find_section_elements(
        self, 
        document_root: Element, 
        section_title: str, 
        section_level: int = 1,
        title_pattern: Optional[str] = None
    ) -> Optional[SectionInfo]:
        """查找章节XML元素 - 精确匹配"""
        
        # 获取当前级别的所有可能样式ID
        possible_styles = Config.HEADING_STYLES_MAP.get(section_level, [f'Heading {section_level}'])
        
        xpath_queries = []
        for style_id in possible_styles:
            xpath_queries.append(f".//w:p[w:pPr/w:pStyle[@w:val='{style_id}']]")
        
        section_paragraphs = []
        for xpath in xpath_queries:
            elements = document_root.xpath(xpath, namespaces=self.namespaces)
            section_paragraphs.extend(elements)
        
        # 查找匹配的标题段落
        target_paragraph = None
        
        if title_pattern:
            pattern = re.compile(title_pattern, re.IGNORECASE | re.MULTILINE)
        else:
            # 构建智能匹配模式
            escaped_title = re.escape(section_title)
            patterns = [
                f"^\\s*{escaped_title}\\s*$",
                f"^\\s*.*{escaped_title}.*\\s*$",
                f"^\\s*\\d+\\.?\\s*{escaped_title}\\s*$",
                f"^\\s*第.*章.*{escaped_title}.*$",
                f"^\\s*Chapter.*{escaped_title}.*$"
            ]
            pattern = re.compile('|'.join(f'({p})' for p in patterns), re.IGNORECASE | re.MULTILINE)
        
        for para in section_paragraphs:
            text_content = self._extract_paragraph_text(para)
            text_stripped = text_content.strip()
            
            if pattern.search(text_stripped):
                target_paragraph = para
                break
        
        if target_paragraph is None:
            return None
        
        # 查找章节范围内的所有XML元素
        section_elements = self._collect_section_xml_elements(
            document_root, target_paragraph, section_level
        )
        
        # 分析使用的样式和关系
        section_info = SectionInfo(
            start_element=target_paragraph,
            end_element=None,
            xml_elements=section_elements,
            title=self._extract_paragraph_text(target_paragraph).strip(),
            level=section_level
        )
        
        # 分析依赖关系
        self._analyze_section_dependencies(section_info)
        
        # 分析结果摘要
        self.logger.debug(f"已分析章节: {len(section_info.xml_elements)}个元素, {len(section_info.used_styles)}个样式, {len(section_info.numbering_references)}个编号")
        
        return section_info
    
    def _extract_paragraph_text(self, paragraph_element: Element) -> str:
        """提取段落纯文本内容"""
        text_parts = []
        
        # 查找所有文本节点
        text_elements = paragraph_element.xpath('.//w:t', namespaces=self.namespaces)
        for t_elem in text_elements:
            if t_elem.text:
                text_parts.append(t_elem.text)
        
        return ''.join(text_parts)
    
    def _collect_section_xml_elements(
        self, 
        document_root: Element, 
        start_paragraph: Element, 
        section_level: int
    ) -> List[Element]:
        """收集章节范围内的所有XML元素"""
        
        body = document_root.find('.//w:body', namespaces=self.namespaces)
        if body is None:
            return []
        
        # 获取所有body子元素
        all_elements = list(body)
        
        # 找到起始段落的位置
        try:
            start_index = all_elements.index(start_paragraph)
        except ValueError:
            return []
        
        # 查找结束位置
        end_index = len(all_elements)
        
        for i in range(start_index + 1, len(all_elements)):
            element = all_elements[i]
            
            if element.tag == f"{{{self.namespaces['w']}}}p":
                # 检查是否是同级或更高级标题
                if self._is_section_boundary(element, section_level):
                    end_index = i
                    break
        
        return all_elements[start_index:end_index]
    
    def _is_section_boundary(self, paragraph_element: Element, current_level: int) -> bool:
        """判断是否为章节边界"""
        
        # 查找段落样式
        style_elements = paragraph_element.xpath('.//w:pStyle', namespaces=self.namespaces)
        
        for style_elem in style_elements:
            style_val = style_elem.get(f"{{{self.namespaces['w']}}}val", "")
            
            # 检查是否是标题样式，并获取级别
            for level, style_list in Config.HEADING_STYLES_MAP.items():
                if style_val in style_list:
                    # 如果找到的标题级别小于等于当前级别，则为边界
                    return level <= current_level
            
            # 兜底：检查传统的标题样式模式
            heading_patterns = [
                r'Heading\s*(\d+)',
                r'标题\s*(\d+)',
                r'Heading(\d+)'
            ]
            
            for pattern in heading_patterns:
                match = re.match(pattern, style_val, re.IGNORECASE)
                if match:
                    level = int(match.group(1))
                    return level <= current_level
        
        return False
    
    def _analyze_section_dependencies(self, section_info: SectionInfo) -> None:
        """分析章节的样式和关系依赖"""
        
        for element in section_info.xml_elements:
            # 分析样式引用
            self._collect_style_references(element, section_info.used_styles)
            
            # 分析关系引用
            self._collect_relationship_references(element, section_info.used_relationships)
            
            # 分析媒体引用
            self._collect_media_references(element, section_info.media_references)
            
            # 分析编号引用
            self._collect_numbering_references(element, section_info.numbering_references)
    
    def _collect_style_references(self, element: Element, style_set: Set[str]) -> None:
        """收集样式引用"""
        
        # 查找所有样式引用
        style_elements = element.xpath('.//w:pStyle | .//w:rStyle | .//w:tblStyle', 
                                     namespaces=self.namespaces)
        
        for style_elem in style_elements:
            style_val = style_elem.get(f"{{{self.namespaces['w']}}}val")
            if style_val:
                style_set.add(style_val)
    
    def _collect_relationship_references(self, element: Element, rel_set: Set[str]) -> None:
        """收集关系引用"""
        
        # 查找所有关系ID引用
        rel_elements = element.xpath('.//*[@r:id]', namespaces=self.namespaces)
        
        for rel_elem in rel_elements:
            rel_id = rel_elem.get(f"{{{self.namespaces['r']}}}id")
            if rel_id:
                rel_set.add(rel_id)
    
    def _collect_media_references(self, element: Element, media_set: Set[str]) -> None:
        """收集媒体文件引用"""
        
        # 使用配置化的媒体查询模式
        media_queries = Config.MEDIA_QUERY_PATTERNS
        
        for query in media_queries:
            try:
                media_elements = element.xpath(query, namespaces=self.namespaces)
                for media_elem in media_elements:
                    embed_id = (media_elem.get(f"{{{self.namespaces['r']}}}embed") or 
                               media_elem.get(f"{{{self.namespaces['r']}}}id"))
                    if embed_id:
                        media_set.add(embed_id)
            except Exception:
                continue
    
    def _collect_numbering_references(self, element: Element, numbering_set: Set[str]) -> None:
        """收集编号引用 - 修复版本，确保编号完整性"""
        
        # 查找段落编号引用
        numbering_elements = element.xpath('.//w:numPr', namespaces=self.namespaces)
        
        for num_elem in numbering_elements:
            # 获取编号ID (numId)
            num_id_elem = num_elem.xpath('./w:numId', namespaces=self.namespaces)
            if num_id_elem:
                num_id = num_id_elem[0].get(f"{{{self.namespaces['w']}}}val")
                if num_id:
                    numbering_set.add(num_id)
                    # 标记需要验证编号定义完整性
                    numbering_set.add(f"verify_{num_id}")
                    
            # 获取抽象编号ID (abstractNumId) - 通常通过numId间接引用
            abstract_num_elem = num_elem.xpath('.//w:abstractNumId', namespaces=self.namespaces)
            if abstract_num_elem:
                abstract_id = abstract_num_elem[0].get(f"{{{self.namespaces['w']}}}val")
                if abstract_id:
                    numbering_set.add(f"abstract_{abstract_id}")


class XMLValidator:
    """XML验证和错误恢复工具"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @handle_exceptions(default_return={'valid': False, 'errors': ['Validation failed'], 'warnings': [], 'structure_info': {}})
    def validate_docx_structure(self, docx_path: Union[str, Path]) -> Dict[str, Any]:
        """验证DOCX文件结构完整性"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'structure_info': {}
        }
        
        try:
            with zipfile.ZipFile(docx_path, 'r') as docx_zip:
                file_list = docx_zip.namelist()
                
                # 检查必需文件
                required_files = Config.REQUIRED_DOCX_FILES
                
                for required_file in required_files:
                    if required_file not in file_list:
                        validation_result['errors'].append(f"Missing required file: {required_file}")
                        validation_result['valid'] = False
                
                # 检查XML格式
                try:
                    document_xml = docx_zip.read('word/document.xml')
                    etree.fromstring(document_xml)
                    validation_result['structure_info']['document_xml_valid'] = True
                except etree.XMLSyntaxError as e:
                    validation_result['errors'].append(f"Invalid document.xml: {e}")
                    validation_result['valid'] = False
                except KeyError:
                    pass
                
                # 检查样式文件
                if 'word/styles.xml' in file_list:
                    try:
                        styles_xml = docx_zip.read('word/styles.xml')
                        etree.fromstring(styles_xml)
                        validation_result['structure_info']['styles_xml_valid'] = True
                    except etree.XMLSyntaxError as e:
                        validation_result['warnings'].append(f"Invalid styles.xml: {e}")
                else:
                    validation_result['warnings'].append("No styles.xml found")
                
                validation_result['structure_info']['total_files'] = len(file_list)
                validation_result['structure_info']['media_files'] = len([f for f in file_list if f.startswith('word/media/')])
                
        except zipfile.BadZipFile:
            validation_result['errors'].append("Not a valid ZIP file")
            validation_result['valid'] = False
        except Exception as e:
            validation_result['errors'].append(f"Validation error: {e}")
            validation_result['valid'] = False
        
        return validation_result
    
    @handle_exceptions()
    def repair_xml_structure(self, xml_data: bytes) -> Tuple[bytes, List[str]]:
        """尝试修复损坏的XML结构"""
        repair_log = []
        
        try:
            # 首先尝试正常解析
            etree.fromstring(xml_data)
            return xml_data, repair_log
        except etree.XMLSyntaxError as e:
            repair_log.append(f"XML error detected: {e}")
            self.logger.warning(f"XML error detected: {e}")
            
            try:
                # 使用恢复模式解析
                parser = etree.XMLParser(recover=True)
                root = etree.fromstring(xml_data, parser)
                
                # 重新序列化
                repaired_xml = etree.tostring(root, encoding='utf-8', xml_declaration=True)
                repair_log.append("XML structure repaired using recovery mode")
                self.logger.info("XML structure repaired using recovery mode")
                
                return repaired_xml, repair_log
            except Exception as repair_error:
                repair_log.append(f"Failed to repair XML: {repair_error}")
                self.logger.error(f"Failed to repair XML: {repair_error}")
                raise WordCopyError(f"Cannot repair XML structure: {repair_error}")


class WordDocumentXMLCopier:
    """
    Word文档XML复制器 - 主要接口
    提供高级的章节复制功能，保持100%格式一致性
    """
    
    def __init__(self, enable_validation: bool = True, enable_repair: bool = True) -> None:
        self.processor = XMLDocumentProcessor()
        self.validator = XMLValidator() if enable_validation else None
        self.enable_repair = enable_repair
        self._temp_dir: Optional[str] = None
        self._numbering_fully_copied = False  # 标记编号是否被完整复制
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @handle_exceptions(default_return=False)
    def copy_section_xml(
        self,
        source_path: Union[str, Path],
        target_path: Union[str, Path],
        section_title: str,
        section_level: int = 1,
        output_path: Optional[Union[str, Path]] = None,
        title_pattern: Optional[str] = None
    ) -> bool:
        """
        使用XML操作复制章节，100%保持格式，包含验证和错误恢复
        
        Args:
            source_path: 源文档路径
            target_path: 目标文档路径
            section_title: 章节标题
            section_level: 章节级别
            output_path: 输出路径，默认覆盖target_path
            title_pattern: 自定义标题匹配模式
            
        Returns:
            bool: 是否成功
        """
        
        try:
            # 重置状态标记
            self._numbering_fully_copied = False
            
            # 预先验证文档结构
            if self.validator:
                source_validation = self.validator.validate_docx_structure(source_path)
                target_validation = self.validator.validate_docx_structure(target_path)
                
                if not source_validation['valid']:
                    self.logger.error(f"Source document validation errors: {source_validation['errors']}")
                    if not self.enable_repair:
                        raise WordCopyError("Source document is invalid and repair is disabled")
                
                if not target_validation['valid']:
                    self.logger.error(f"Target document validation errors: {target_validation['errors']}")
                    if not self.enable_repair:
                        raise WordCopyError("Target document is invalid and repair is disabled")
            
            # 提取源文档结构（带错误恢复）
            source_structure = self._extract_with_recovery(source_path)
            target_structure = self._extract_with_recovery(target_path)
            
            # 解析XML（带错误恢复）
            source_doc = self._parse_with_recovery(source_structure.document_xml, "source document")
            target_doc = self._parse_with_recovery(target_structure.document_xml, "target document")
            
            # 查找章节
            section_info = self.processor.find_section_elements(
                source_doc, section_title, section_level, title_pattern
            )
            
            if not section_info:
                raise WordCopyError(f"Section '{section_title}' not found")
            
            self.logger.debug(f"找到章节: '{section_info.title}' ({len(section_info.xml_elements)} 个元素)")
            
            # 复制章节XML元素
            self._copy_xml_elements_with_dependencies(
                section_info, source_structure, target_structure, target_doc
            )
            
            # 生成新文档
            output_file = output_path or target_path
            self._create_output_document(target_structure, target_doc, output_file)
            
            # 验证输出文档
            if self.validator:
                output_validation = self.validator.validate_docx_structure(output_file)
                if not output_validation['valid']:
                    self.logger.debug("警告: 输出文档有验证问题")
                else:
                    self.logger.debug("输出文档验证成功")
            
            return True
            
        except Exception as e:
            raise RuntimeError(f"Failed to copy section: {e}") from e
        finally:
            self._cleanup_temp_files()
    
    def _extract_with_recovery(self, docx_path: Union[str, Path]) -> DocumentStructure:
        """带错误恢复的文档结构提取"""
        try:
            return self.processor.extract_document_structure(docx_path)
        except Exception as e:
            if self.enable_repair:
                self.logger.debug("尝试恢复文档结构")
                try:
                    # 尝试基本的文档结构提取，忽略非关键文件
                    return self._extract_minimal_structure(docx_path)
                except Exception:
                    self.logger.debug("恢复失败")
                    raise e
            else:
                raise e
    
    def _extract_minimal_structure(self, docx_path: Union[str, Path]) -> DocumentStructure:
        """提取最小化的文档结构（仅关键文件）"""
        docx_path = Path(docx_path)
        
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            # 只提取最关键的文件
            try:
                document_xml = docx_zip.read('word/document.xml')
            except KeyError:
                raise WordCopyError("Cannot extract minimal structure: missing word/document.xml")
            
            # 创建最小结构
            structure = DocumentStructure(document_xml=document_xml)
            
            # 尝试提取其他文件，但失败时不报错
            try:
                structure.styles_xml = docx_zip.read('word/styles.xml')
            except KeyError:
                self.logger.debug("警告: 未找到 styles.xml")
            
            try:
                structure.content_types = docx_zip.read('[Content_Types].xml')
            except KeyError:
                self.logger.debug("警告: 未找到 [Content_Types].xml")
            
            # 提取关系文件（尽力而为）
            for file_path in docx_zip.namelist():
                try:
                    if file_path.endswith('.rels'):
                        structure.relationships[file_path] = docx_zip.read(file_path)
                    elif file_path.startswith('word/media/'):
                        structure.media_files[file_path] = docx_zip.read(file_path)
                except Exception:
                    pass  # 提取失败
                    continue
        
        return structure
    
    def _parse_with_recovery(self, xml_data: bytes, context: str) -> Element:
        """带错误恢复的XML解析"""
        try:
            return self.processor.parse_document_xml(xml_data)
        except Exception as e:
            if self.enable_repair and self.validator:
                self.logger.debug(f"Attempting to repair {context} XML: {e}")
                repaired_xml, repair_log = self.validator.repair_xml_structure(xml_data)
                if repair_log:
                    self.logger.debug(f"Repair log for {context}: {repair_log}")
                return self.processor.parse_document_xml(repaired_xml)
            else:
                raise e
    
    def _copy_xml_elements_with_dependencies(
        self,
        section_info: SectionInfo,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure,
        target_doc: Element
    ) -> None:
        """复制XML元素及其依赖关系"""
        
        # 获取目标文档的body元素
        target_body = target_doc.find('.//w:body', namespaces=self.processor.namespaces)
        if target_body is None:
            raise WordCopyError("Target document has invalid structure")
        
        # 深度复制所有章节元素，特别处理编号段落  
        self.logger.debug(f"\n开始复制 {len(section_info.xml_elements)} 个元素...")
        
        for i, element in enumerate(section_info.xml_elements):
            # 检查是否为段落元素
            if element.tag == f"{{{self.processor.namespaces['w']}}}p":
                # 检查是否包含编号
                num_pr = element.xpath('.//w:numPr', namespaces=self.processor.namespaces)
                if num_pr:
                    pass  # 编号段落，已处理
            
            # 创建元素的完整副本，保持所有属性和命名空间
            copied_element = self._deep_copy_xml_element(element)
            
            # 检查并修复编号段落的重启问题
            if element.tag == f"{{{self.processor.namespaces['w']}}}p":
                self._ensure_numbering_restart(copied_element, i, section_info.xml_elements)
            
            # 在复制后验证编号信息是否保持
            if element.tag == f"{{{self.processor.namespaces['w']}}}p":
                copied_num_pr = copied_element.xpath('.//w:numPr', namespaces=self.processor.namespaces)
                if copied_num_pr:
                    pass
            
            target_body.append(copied_element)
        
        # 同步样式定义
        if section_info.used_styles and source_structure.styles_xml:
            self._sync_style_definitions(
                section_info.used_styles,
                source_structure.styles_xml,
                target_structure
            )
        
        # 同步编号定义
        if section_info.numbering_references:
            if source_structure.numbering_xml:
                self.logger.debug(f"同步编号定义: {len(section_info.numbering_references)} 个引用")
                self._sync_numbering_definitions(
                    section_info.numbering_references,
                    source_structure.numbering_xml,
                    target_structure
                )
            else:
                self.logger.debug("警告: 源文档编号文件缺失")
        
        # 检测模板兼容性并进行增强同步
        compatibility_info = self._assess_template_compatibility(source_structure, target_structure)
        # 如果编号定义被完整复制，更新兼容性状态
        if hasattr(self, '_numbering_fully_copied') and self._numbering_fully_copied:
            compatibility_info['numbering_synced'] = True
            self.logger.debug("编号定义已完整复制，提升模板兼容性")
        self._enhance_format_compatibility(source_structure, target_structure, compatibility_info)
        
        # 同步关系和媒体文件
        if section_info.used_relationships or section_info.media_references:
            self._sync_relationships_and_media(
                section_info,
                source_structure,
                target_structure
            )
    
    def _deep_copy_xml_element(self, element: Element) -> Element:
        """深度复制XML元素，保持所有属性和命名空间"""
        
        # 使用copy.deepcopy保持完整结构
        # lxml的Element支持deepcopy操作
        copied = copy.deepcopy(element)
        
        # 确保命名空间前缀正确保持
        self._preserve_namespace_prefixes(copied)
        
        return copied
    
    def _ensure_numbering_restart(self, paragraph_element: Element, element_index: int, all_elements: List[Element]) -> None:
        """确保编号段落的重启设置正确"""
        
        # 检查是否包含编号
        num_pr_elements = paragraph_element.xpath('.//w:numPr', namespaces=self.processor.namespaces)
        if not num_pr_elements:
            return
        
        num_pr = num_pr_elements[0]
        
        # 获取当前段落的编号ID
        num_id_elem = num_pr.xpath('./w:numId', namespaces=self.processor.namespaces)
        if not num_id_elem:
            return
        
        current_num_id = num_id_elem[0].get(f"{{{self.processor.namespaces['w']}}}val", "")
        current_ilvl_elem = num_pr.xpath('./w:ilvl', namespaces=self.processor.namespaces)
        current_ilvl = current_ilvl_elem[0].get(f"{{{self.processor.namespaces['w']}}}val", "0") if current_ilvl_elem else "0"
        
        # 检查编号重启
        
        # 检查是否是该编号序列的第一个段落
        is_first_in_sequence = True
        
        # 向前查找相同numId和级别的段落
        for i in range(element_index - 1, -1, -1):
            prev_element = all_elements[i]
            if prev_element.tag == f"{{{self.processor.namespaces['w']}}}p":
                prev_num_pr = prev_element.xpath('.//w:numPr', namespaces=self.processor.namespaces)
                if prev_num_pr:
                    prev_num_id_elem = prev_num_pr[0].xpath('./w:numId', namespaces=self.processor.namespaces)
                    prev_ilvl_elem = prev_num_pr[0].xpath('./w:ilvl', namespaces=self.processor.namespaces)
                    
                    if prev_num_id_elem:
                        prev_num_id = prev_num_id_elem[0].get(f"{{{self.processor.namespaces['w']}}}val", "")
                        prev_ilvl = prev_ilvl_elem[0].get(f"{{{self.processor.namespaces['w']}}}val", "0") if prev_ilvl_elem else "0"
                        
                        if prev_num_id == current_num_id and prev_ilvl == current_ilvl:
                            is_first_in_sequence = False
                            break
        
        if is_first_in_sequence:
            # 添加编号重启属性
            
            # 检查是否已有重启属性
            existing_restart = num_pr.xpath('./w:numRestart', namespaces=self.processor.namespaces)
            if not existing_restart:
                # 添加重启属性
                restart_elem = etree.Element(f"{{{self.processor.namespaces['w']}}}numRestart")  
                restart_elem.set(f"{{{self.processor.namespaces['w']}}}val", "1")
                
                # 插入到适当位置（在ilvl之后，numId之前）
                ilvl_elem = num_pr.xpath('./w:ilvl', namespaces=self.processor.namespaces)
                if ilvl_elem:
                    # 在ilvl后面插入
                    ilvl_index = list(num_pr).index(ilvl_elem[0])
                    num_pr.insert(ilvl_index + 1, restart_elem)
                else:
                    # 如果没有ilvl，插入到开头
                    num_pr.insert(0, restart_elem)
                
                pass  # 已添加 numRestart
            else:
                pass  # 重启属性已存在
        else:
            # 编号序列延续
            pass
    
    def _preserve_namespace_prefixes(self, element: Element) -> None:
        """保持命名空间前缀"""
        
        # 递归处理所有子元素
        for child in element.iter():
            # 确保元素和属性的命名空间前缀正确
            if child.prefix:
                # 已有前缀，保持不变
                continue
            
            # 检查是否需要添加前缀
            if child.nsmap:
                for prefix, uri in child.nsmap.items():
                    if uri in self.processor.namespaces.values():
                        # 找到对应的标准前缀
                        for std_prefix, std_uri in self.processor.namespaces.items():
                            if std_uri == uri and prefix != std_prefix:
                                # 需要标准化前缀 - 但要小心不破坏现有结构
                                pass
    
    def _sync_style_definitions(
        self,
        used_styles: Set[str],
        source_styles_xml: bytes,
        target_structure: DocumentStructure
    ) -> None:
        """同步样式定义，包括依赖关系处理"""
        
        if not target_structure.styles_xml:
            # 目标文档没有样式文件，直接复制源样式
            target_structure.styles_xml = source_styles_xml
            self.logger.debug("复制样式文件")
            return
        
        try:
            # 解析源和目标样式XML
            source_styles = self.processor.parse_document_xml(source_styles_xml)
            target_styles = self.processor.parse_document_xml(target_structure.styles_xml)
            
            # 收集所有需要的样式（包括依赖的样式）
            all_needed_styles = self._collect_style_dependencies(used_styles, source_styles)
            self.logger.debug(f"样式依赖: {len(all_needed_styles)} 个")
            
            copied_count = 0
            
            for style_id in all_needed_styles:
                # 查找源样式定义
                source_style = source_styles.xpath(
                    f".//w:style[@w:styleId='{style_id}']",
                    namespaces=self.processor.namespaces
                )
                
                if source_style:
                    # 检查目标是否已有此样式
                    existing_style = target_styles.xpath(
                        f".//w:style[@w:styleId='{style_id}']",
                        namespaces=self.processor.namespaces
                    )
                    
                    if not existing_style:
                        # 目标没有此样式，直接复制
                        copied_style = self._deep_copy_xml_element(source_style[0])
                        target_styles.append(copied_style)
                        copied_count += 1
                        pass  # 复制样式
                    else:
                        # 目标已有此样式，比较定义是否相同
                        if self._should_override_style(source_style[0], existing_style[0], style_id):
                            # 替换现有样式定义
                            parent = existing_style[0].getparent()
                            parent.remove(existing_style[0])
                            copied_style = self._deep_copy_xml_element(source_style[0])
                            parent.append(copied_style)
                            copied_count += 1
                            pass  # 覆盖样式
                        else:
                            pass  # 跳过相同样式
            
            self.logger.debug(f"样式同步完成: {copied_count} 个")
            
            # 更新目标样式XML
            target_structure.styles_xml = etree.tostring(
                target_styles, 
                encoding='utf-8', 
                xml_declaration=True,
                pretty_print=True
            )
            
        except Exception as e:
            self.logger.error(f"Failed to sync styles: {e}")
            import traceback
            traceback.print_exc()
    
    def _collect_style_dependencies(self, used_styles: Set[str], source_styles: Element) -> Set[str]:
        """收集样式依赖关系 - 包括基础样式和链接样式"""
        
        all_styles = set(used_styles)
        styles_to_check = list(used_styles)
        
        while styles_to_check:
            current_style = styles_to_check.pop(0)
            
            # 查找当前样式定义
            style_element = source_styles.xpath(
                f".//w:style[@w:styleId='{current_style}']",
                namespaces=self.processor.namespaces
            )
            
            if not style_element:
                continue
                
            style_elem = style_element[0]
            
            # 查找基础样式 (basedOn)
            based_on = style_elem.xpath(
                './/w:basedOn/@w:val',
                namespaces=self.processor.namespaces
            )
            
            for base_style in based_on:
                if base_style not in all_styles:
                    all_styles.add(base_style)
                    styles_to_check.append(base_style)
                    pass  # 基础样式依赖
            
            # 查找下一个样式 (next)  
            next_style = style_elem.xpath(
                './/w:next/@w:val',
                namespaces=self.processor.namespaces
            )
            
            for next_st in next_style:
                if next_st not in all_styles:
                    all_styles.add(next_st)
                    styles_to_check.append(next_st)
                    pass  # 链接样式依赖
            
            # 查找链接样式 (link)
            linked_style = style_elem.xpath(
                './/w:link/@w:val',
                namespaces=self.processor.namespaces
            )
            
            for link_st in linked_style:
                if link_st not in all_styles:
                    all_styles.add(link_st)
                    styles_to_check.append(link_st)
                    pass  # 链接样式依赖
        
        return all_styles
    
    def _should_override_style(self, source_style: Element, target_style: Element, style_id: str) -> bool:
        """判断是否应该用源样式覆盖目标样式"""
        
        # 对于标题样式，更倾向于覆盖以保持格式一致性
        heading_patterns = ['Heading', '标题', r'^\d+$']  # 包括数字ID的标题样式
        is_heading = any(pattern in style_id or (pattern.startswith('^') and re.match(pattern, style_id)) 
                        for pattern in heading_patterns)
        
        if is_heading:
            pass  # 标题样式覆盖
            return True
        
        # 比较关键格式属性
        differences = []
        
        # 比较字体属性
        source_fonts = self._extract_font_info(source_style)
        target_fonts = self._extract_font_info(target_style)
        if source_fonts != target_fonts:
            differences.append(f"字体: {target_fonts} -> {source_fonts}")
        
        # 比较字号
        source_size = self._extract_font_size(source_style)
        target_size = self._extract_font_size(target_style)
        if source_size != target_size:
            differences.append(f"字号: {target_size} -> {source_size}")
        
        # 比较段落间距
        source_spacing = self._extract_paragraph_spacing(source_style)
        target_spacing = self._extract_paragraph_spacing(target_style)
        if source_spacing != target_spacing:
            differences.append(f"段落间距: {target_spacing} -> {source_spacing}")
            
        # 比较对齐方式
        source_align = self._extract_alignment(source_style)
        target_align = self._extract_alignment(target_style)
        if source_align != target_align:
            differences.append(f"对齐: {target_align} -> {source_align}")
        
        if differences:
            pass  # 样式差异
            return True
        
        return False
    
    def _extract_font_info(self, style_element: Element) -> Dict[str, str]:
        """提取字体信息"""
        fonts = {}
        font_elements = style_element.xpath('.//w:rFonts', namespaces=self.processor.namespaces)
        
        if font_elements:
            font_elem = font_elements[0]
            fonts['ascii'] = font_elem.get(f"{{{self.processor.namespaces['w']}}}ascii", '')
            fonts['eastAsia'] = font_elem.get(f"{{{self.processor.namespaces['w']}}}eastAsia", '')
            fonts['hAnsi'] = font_elem.get(f"{{{self.processor.namespaces['w']}}}hAnsi", '')
        
        return fonts
    
    def _extract_font_size(self, style_element: Element) -> str:
        """提取字号信息"""
        size_elements = style_element.xpath('.//w:sz', namespaces=self.processor.namespaces)
        return size_elements[0].get(f"{{{self.processor.namespaces['w']}}}val", '') if size_elements else ''
    
    def _extract_paragraph_spacing(self, style_element: Element) -> Dict[str, str]:
        """提取段落间距信息"""
        spacing = {}
        spacing_elements = style_element.xpath('.//w:spacing', namespaces=self.processor.namespaces)
        
        if spacing_elements:
            spacing_elem = spacing_elements[0]
            spacing['before'] = spacing_elem.get(f"{{{self.processor.namespaces['w']}}}before", '')
            spacing['after'] = spacing_elem.get(f"{{{self.processor.namespaces['w']}}}after", '')
            spacing['line'] = spacing_elem.get(f"{{{self.processor.namespaces['w']}}}line", '')
        
        return spacing
    
    def _extract_alignment(self, style_element: Element) -> str:
        """提取对齐方式"""
        align_elements = style_element.xpath('.//w:jc', namespaces=self.processor.namespaces)
        return align_elements[0].get(f"{{{self.processor.namespaces['w']}}}val", '') if align_elements else ''
    
    def _sync_numbering_definitions(
        self,
        used_numbering: Set[str],
        source_numbering_xml: bytes,
        target_structure: DocumentStructure
    ) -> None:
        """同步编号定义 - 修复版本，确保编号完整性"""
        
        if not target_structure.numbering_xml:
            # 目标文档没有编号文件，直接复制源文档的完整编号定义
            self.logger.debug("目标文档缺少编号定义文件，复制源文档完整编号定义")
            target_structure.numbering_xml = source_numbering_xml
            # 设置标记表示编号定义被完整复制
            self._numbering_fully_copied = True
            self.logger.debug("编号定义复制完成")
            return
        
        try:
            # 解析源和目标编号XML
            source_numbering = self.processor.parse_document_xml(source_numbering_xml)
            target_numbering = self.processor.parse_document_xml(target_structure.numbering_xml)
            
            self.logger.debug(f"编号同步: {len(used_numbering)} 个")
            
            # 收集需要复制的编号定义 - 增强版本
            copied_count = 0
            all_needed_nums = self._collect_all_numbering_dependencies(used_numbering, source_numbering)
            # 包含依赖的编号数: {len(all_needed_nums)}
            
            for num_ref in all_needed_nums:
                # 跳过验证标记 - 它们已经在依赖收集阶段处理了
                if num_ref.startswith('verify_'):
                    continue
                
                # 处理抽象编号引用
                if num_ref.startswith('abstract_'):
                    abstract_id = num_ref.replace('abstract_', '')
                    source_abstract = source_numbering.xpath(
                        f".//w:abstractNum[@w:abstractNumId='{abstract_id}']",
                        namespaces=self.processor.namespaces
                    )
                    
                    if source_abstract:
                        # 检查目标是否已有此抽象编号
                        existing_abstract = target_numbering.xpath(
                            f".//w:abstractNum[@w:abstractNumId='{abstract_id}']",
                            namespaces=self.processor.namespaces
                        )
                        
                        if not existing_abstract:
                            copied_abstract = self._deep_copy_xml_element(source_abstract[0])
                            target_numbering.append(copied_abstract)
                            copied_count += 1
                            pass  # 复制抽象编号
                
                elif num_ref.startswith('virtual_'):
                    # 处理虚拟编号引用（为numId=0特殊创建）
                    virtual_id = num_ref.replace('virtual_', '')
                    pass  # 处理虚拟编号
                    
                    # 检查是否已有numId=0的编号实例
                    existing_num = target_numbering.xpath(
                        f".//w:num[@w:numId='{virtual_id}']",
                        namespaces=self.processor.namespaces
                    )
                    
                    if not existing_num:
                        # 创建一个编号实例，使用第一个可用的抽象编号
                        available_abstracts = target_numbering.xpath(
                            './/w:abstractNum',
                            namespaces=self.processor.namespaces
                        )
                        
                        if available_abstracts:
                            first_abstract_id = available_abstracts[0].get(f"{{{self.processor.namespaces['w']}}}abstractNumId", '0')
                            virtual_num = self._create_virtual_numbering_instance(virtual_id, first_abstract_id)
                            target_numbering.append(virtual_num)
                            copied_count += 1
                            pass  # 创建虚拟编号
                        else:
                            self.logger.debug(f"警告: 无法创建虚拟编号 {virtual_id}")
                
                else:
                    # 处理具体编号引用 (numId)
                    source_num = source_numbering.xpath(
                        f".//w:num[@w:numId='{num_ref}']",
                        namespaces=self.processor.namespaces
                    )
                    
                    if source_num:
                        source_num_elem = source_num[0]
                        
                        # 获取源编号引用的抽象编号ID
                        source_abstract_refs = source_num_elem.xpath(
                            './/w:abstractNumId/@w:val',
                            namespaces=self.processor.namespaces
                        )
                        
                        if not source_abstract_refs:
                            self.logger.debug(f"警告: 编号 {num_ref} 无抽象引用")
                            continue
                        
                        source_abstract_id = source_abstract_refs[0]
                        
                        # 检查目标是否已有此编号
                        existing_num = target_numbering.xpath(
                            f".//w:num[@w:numId='{num_ref}']",
                            namespaces=self.processor.namespaces
                        )
                        
                        should_copy_or_replace = True
                        
                        if existing_num:
                            # 如果目标已有此编号，检查其引用的抽象编号是否正确
                            existing_abstract_refs = existing_num[0].xpath(
                                './/w:abstractNumId/@w:val',
                                namespaces=self.processor.namespaces
                            )
                            
                            if existing_abstract_refs and existing_abstract_refs[0] == source_abstract_id:
                                # 抽象编号引用匹配，检查抽象编号定义是否一致
                                target_abstract = target_numbering.xpath(
                                    f".//w:abstractNum[@w:abstractNumId='{source_abstract_id}']",
                                    namespaces=self.processor.namespaces
                                )
                                source_abstract = source_numbering.xpath(
                                    f".//w:abstractNum[@w:abstractNumId='{source_abstract_id}']",
                                    namespaces=self.processor.namespaces
                                )
                                
                                if target_abstract and source_abstract:
                                    # 比较抽象编号定义是否一致
                                    if self._compare_abstract_numbering(source_abstract[0], target_abstract[0]):
                                        pass  # 编号已存在
                                        should_copy_or_replace = False
                                    else:
                                        pass  # 编号格式不一致
                                        # 先删除现有的编号实例
                                        existing_num[0].getparent().remove(existing_num[0])
                                else:
                                    pass  # 抽象编号不存在
                            else:
                                pass  # 编号引用不同
                                # 先删除现有的编号实例
                                existing_num[0].getparent().remove(existing_num[0])
                        
                        if should_copy_or_replace:
                            # 复制编号实例
                            copied_num = self._deep_copy_xml_element(source_num_elem)
                            target_numbering.append(copied_num)
                            copied_count += 1
                            pass  # 复制编号定义
                            
                            # 确保对应的抽象编号存在
                            source_abstract = source_numbering.xpath(
                                f".//w:abstractNum[@w:abstractNumId='{source_abstract_id}']",
                                namespaces=self.processor.namespaces
                            )
                            
                            if source_abstract:
                                existing_abstract = target_numbering.xpath(
                                    f".//w:abstractNum[@w:abstractNumId='{source_abstract_id}']",
                                    namespaces=self.processor.namespaces
                                )
                                
                                if not existing_abstract:
                                    copied_abstract = self._deep_copy_xml_element(source_abstract[0])
                                    target_numbering.append(copied_abstract)
                                    pass  # 复制关联抽象编号
                                elif not self._compare_abstract_numbering(source_abstract[0], existing_abstract[0]):
                                    # 抽象编号存在但定义不同，替换它
                                    existing_abstract[0].getparent().remove(existing_abstract[0])
                                    copied_abstract = self._deep_copy_xml_element(source_abstract[0])
                                    target_numbering.append(copied_abstract)
                                    pass  # 替换抽象编号
                        else:
                            pass  # 编号无需处理
            
            self.logger.debug(f"编号同步完成: {copied_count} 个")
            
            # 更新目标编号XML
            target_structure.numbering_xml = etree.tostring(
                target_numbering,
                encoding='utf-8',
                xml_declaration=True,
                pretty_print=True
            )
            
        except Exception as e:
            self.logger.error(f"Failed to sync numbering: {e}")
            import traceback
            traceback.print_exc()
    
    def _collect_all_numbering_dependencies(self, used_numbering: Set[str], source_numbering: Element) -> Set[str]:
        """收集所有编号依赖关系，确保不遗漏任何编号定义"""
        
        all_nums = set(used_numbering)
        nums_to_check = list(used_numbering)
        
        self.logger.info(f"Processing numbering dependencies: {used_numbering}")
        
        while nums_to_check:
            current_num = nums_to_check.pop(0)
            
            # 处理验证标记 - 修复版本
            if current_num.startswith('verify_'):
                verify_num = current_num.replace('verify_', '')
                self.logger.debug(f"  Verifying numbering definition integrity: {verify_num}")
                
                # 验证编号实例是否存在
                num_element = source_numbering.xpath(
                    f".//w:num[@w:numId='{verify_num}']",
                    namespaces=self.processor.namespaces
                )
                
                if num_element:
                    # 验证抽象编号定义
                    abstract_refs = num_element[0].xpath(
                        './/w:abstractNumId/@w:val',
                        namespaces=self.processor.namespaces
                    )
                    
                    for abstract_id in abstract_refs:
                        abstract_element = source_numbering.xpath(
                            f".//w:abstractNum[@w:abstractNumId='{abstract_id}']",
                            namespaces=self.processor.namespaces
                        )
                        
                        if not abstract_element:
                            self.logger.warning(f"    Missing abstract numbering definition: {abstract_id}")
                        else:
                            # 验证抽象编号的格式定义
                            lvl_elements = abstract_element[0].xpath(
                                './/w:lvl',
                                namespaces=self.processor.namespaces
                            )
                            self.logger.debug(f"    Abstract numbering {abstract_id} has {len(lvl_elements)} level definitions")
                            
                            # 确保抽象编号被包含
                            abstract_key = f"abstract_{abstract_id}"
                            if abstract_key not in all_nums:
                                all_nums.add(abstract_key)
                                self.logger.debug(f"    - Added verified abstract numbering: {abstract_id}")
                else:
                    self.logger.warning(f"    Numbering instance does not exist: {verify_num}")
                
                continue
            
            if current_num.startswith('abstract_'):
                # 已经是抽象编号，不需要进一步处理
                continue
            
            self.logger.debug(f"  Checking numbering ID: {current_num}")
            
            # 特殊处理numId="0" - 它可能不存在但需要创建
            if current_num == '0':
                self.logger.debug(f"    Detected special numbering ID=0, needs special handling")
                
                # 检查是否已有numId=0的编号实例
                num_zero = source_numbering.xpath(
                    './/w:num[@w:numId="0"]',
                    namespaces=self.processor.namespaces
                )
                
                if num_zero:
                    # 如果存在，按正常流程处理
                    abstract_refs = num_zero[0].xpath(
                        './/w:abstractNumId/@w:val',
                        namespaces=self.processor.namespaces
                    )
                    
                    for abstract_id in abstract_refs:
                        abstract_key = f"abstract_{abstract_id}"
                        if abstract_key not in all_nums:
                            all_nums.add(abstract_key)
                            self.logger.debug(f"    - Added abstract numbering referenced by numId=0: {abstract_id}")
                else:
                    # 如果不存在，查找默认的抽象编号定义
                    default_abstract = source_numbering.xpath(
                        './/w:abstractNum[@w:abstractNumId="0"]',
                        namespaces=self.processor.namespaces
                    )
                    if default_abstract:
                        abstract_key = "abstract_0"
                        if abstract_key not in all_nums:
                            all_nums.add(abstract_key)
                            self.logger.debug(f"    - Added default abstract numbering: 0")
                    
                    # 为numId=0创建一个虚拟的编号实例标记
                    all_nums.add('virtual_0')
                    self.logger.debug(f"    - Added virtual numbering marker: virtual_0")
                
                continue
            
            # 查找具体编号定义
            num_element = source_numbering.xpath(
                f".//w:num[@w:numId='{current_num}']",
                namespaces=self.processor.namespaces
            )
            
            if num_element:
                # 获取对应的抽象编号ID
                abstract_refs = num_element[0].xpath(
                    './/w:abstractNumId/@w:val',
                    namespaces=self.processor.namespaces
                )
                
                if abstract_refs:
                    self.logger.debug(f"    Found numbering instance, referencing abstract numbering: {abstract_refs[0]}")
                
                for abstract_id in abstract_refs:
                    abstract_key = f"abstract_{abstract_id}"
                    if abstract_key not in all_nums:
                        all_nums.add(abstract_key)
                        self.logger.debug(f"    - Added dependent abstract numbering: {abstract_id}")
            else:
                self.logger.warning(f"    Numbering instance {current_num} not found, will try to create default numbering")
                # 尝试查找第一个可用的抽象编号
                first_abstract = source_numbering.xpath(
                    './/w:abstractNum[1]',
                    namespaces=self.processor.namespaces
                )
                if first_abstract:
                    abstract_id = first_abstract[0].get(f"{{{self.processor.namespaces['w']}}}abstractNumId")
                    if abstract_id:
                        all_nums.add(f"abstract_{abstract_id}")
                        all_nums.add(f"virtual_{current_num}")
                        self.logger.debug(f"    - Using first abstract numbering to create virtual numbering: {abstract_id}")
        
        self.logger.debug(f"  Final dependency set: {all_nums}")
        return all_nums
    
    def _compare_abstract_numbering(self, source_abstract: Element, target_abstract: Element) -> bool:
        """比较两个抽象编号定义是否一致"""
        
        try:
            # 比较关键的编号格式属性
            source_levels = source_abstract.xpath('.//w:lvl', namespaces=self.processor.namespaces)
            target_levels = target_abstract.xpath('.//w:lvl', namespaces=self.processor.namespaces)
            
            if len(source_levels) != len(target_levels):
                return False
            
            # 比较每个级别的编号格式
            for src_lvl, tgt_lvl in zip(source_levels, target_levels):
                # 比较级别索引
                src_ilvl = src_lvl.get(f"{{{self.processor.namespaces['w']}}}ilvl", "")
                tgt_ilvl = tgt_lvl.get(f"{{{self.processor.namespaces['w']}}}ilvl", "")
                if src_ilvl != tgt_ilvl:
                    return False
                
                # 比较编号格式 (numFmt)
                src_fmt = src_lvl.xpath('.//w:numFmt/@w:val', namespaces=self.processor.namespaces)
                tgt_fmt = tgt_lvl.xpath('.//w:numFmt/@w:val', namespaces=self.processor.namespaces)
                if src_fmt != tgt_fmt:
                    return False
                
                # 比较编号文本模板 (lvlText)
                src_text = src_lvl.xpath('.//w:lvlText/@w:val', namespaces=self.processor.namespaces)
                tgt_text = tgt_lvl.xpath('.//w:lvlText/@w:val', namespaces=self.processor.namespaces)
                if src_text != tgt_text:
                    return False
                
                # 比较起始值 (start)
                src_start = src_lvl.xpath('.//w:start/@w:val', namespaces=self.processor.namespaces)
                tgt_start = tgt_lvl.xpath('.//w:start/@w:val', namespaces=self.processor.namespaces)
                if src_start != tgt_start:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error comparing abstract numbering: {e}")
            return False
    
    def _create_virtual_numbering_instance(self, num_id: str, abstract_num_id: str) -> Element:
        """为缺失的编号ID创建虚拟编号实例 - 修复版本"""
        
        # 创建 w:num 元素
        num_elem = etree.Element(f"{{{self.processor.namespaces['w']}}}num")
        num_elem.set(f"{{{self.processor.namespaces['w']}}}numId", num_id)
        
        # 创建 w:abstractNumId 元素
        abstract_ref = etree.SubElement(num_elem, f"{{{self.processor.namespaces['w']}}}abstractNumId")
        abstract_ref.set(f"{{{self.processor.namespaces['w']}}}val", abstract_num_id)
        
        if self.logger.isEnabledFor(logging.DEBUG):
            xml_debug = etree.tostring(num_elem, encoding='unicode', pretty_print=True).strip()
            self.logger.debug(f"  Created virtual numbering XML: {xml_debug}")
        
        return num_elem
    
    def _create_default_numbering_xml(self) -> bytes:
        """创建默认的编号XML文件，包含基本的编号定义"""
        
        # 创建根元素
        root = etree.Element(f"{{{self.processor.namespaces['w']}}}numbering")
        
        # 添加默认抽象编号定义
        abstract_num = etree.SubElement(root, f"{{{self.processor.namespaces['w']}}}abstractNum")
        abstract_num.set(f"{{{self.processor.namespaces['w']}}}abstractNumId", "0")
        
        # 添加级别0的定义
        lvl = etree.SubElement(abstract_num, f"{{{self.processor.namespaces['w']}}}lvl")
        lvl.set(f"{{{self.processor.namespaces['w']}}}ilvl", "0")
        
        # 添加编号格式
        num_fmt = etree.SubElement(lvl, f"{{{self.processor.namespaces['w']}}}numFmt")
        num_fmt.set(f"{{{self.processor.namespaces['w']}}}val", "decimal")
        
        # 添加编号文本
        lvl_text = etree.SubElement(lvl, f"{{{self.processor.namespaces['w']}}}lvlText")
        lvl_text.set(f"{{{self.processor.namespaces['w']}}}val", "%1.")
        
        # 添加起始值  
        start = etree.SubElement(lvl, f"{{{self.processor.namespaces['w']}}}start")
        start.set(f"{{{self.processor.namespaces['w']}}}val", "1")
        
        # 添加对齐
        lvl_jc = etree.SubElement(lvl, f"{{{self.processor.namespaces['w']}}}lvlJc")
        lvl_jc.set(f"{{{self.processor.namespaces['w']}}}val", "left")
        
        # 添加段落属性
        p_pr = etree.SubElement(lvl, f"{{{self.processor.namespaces['w']}}}pPr")
        ind = etree.SubElement(p_pr, f"{{{self.processor.namespaces['w']}}}ind")
        ind.set(f"{{{self.processor.namespaces['w']}}}left", "720")
        ind.set(f"{{{self.processor.namespaces['w']}}}hanging", "360")
        
        # 创建编号实例
        num = etree.SubElement(root, f"{{{self.processor.namespaces['w']}}}num")
        num.set(f"{{{self.processor.namespaces['w']}}}numId", "0")
        
        abstract_num_id = etree.SubElement(num, f"{{{self.processor.namespaces['w']}}}abstractNumId")
        abstract_num_id.set(f"{{{self.processor.namespaces['w']}}}val", "0")
        
        return etree.tostring(root, encoding='utf-8', xml_declaration=True, pretty_print=True)
    
    def _sync_format_support_files(
        self,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure
    ) -> None:
        """同步字体表、主题文件等格式支持文件"""
        
        format_files = [
            ('font_table_xml', 'fontTable.xml', '字体表'),
            ('theme_xml', 'theme1.xml', '主题文件'),
            ('settings_xml', 'settings.xml', '文档设置')
        ]
        
        for attr_name, file_name, display_name in format_files:
            source_data = getattr(source_structure, attr_name)
            target_data = getattr(target_structure, attr_name)
            
            if source_data and not target_data:
                # 目标文档缺少此文件，直接复制源文件
                setattr(target_structure, attr_name, source_data)
                self.logger.debug(f"复制 {display_name}: {file_name}")
            elif source_data and target_data:
                # 两个文档都有此文件，检查是否需要合并或替换
                if attr_name == 'font_table_xml':
                    # 字体表可能需要合并
                    merged_font_table = self._merge_font_tables(source_data, target_data)
                    if merged_font_table:
                        target_structure.font_table_xml = merged_font_table
                        self.logger.debug(f"合并 {display_name}")
                else:
                    # 主题和设置文件通常直接使用源文件以保持一致性
                    setattr(target_structure, attr_name, source_data)
                    self.logger.debug(f"替换 {display_name}")
    
    def _merge_font_tables(self, source_font_table: bytes, target_font_table: bytes) -> Optional[bytes]:
        """合并字体表，确保包含所有需要的字体"""
        
        try:
            source_fonts = self.processor.parse_document_xml(source_font_table)
            target_fonts = self.processor.parse_document_xml(target_font_table)
            
            # 获取目标字体表中已有的字体
            existing_fonts = set()
            for font_elem in target_fonts.xpath('.//w:font', namespaces=self.processor.namespaces):
                font_name = font_elem.get(f"{{{self.processor.namespaces['w']}}}name")
                if font_name:
                    existing_fonts.add(font_name)
            
            # 添加源文档中的新字体
            added_count = 0
            for font_elem in source_fonts.xpath('.//w:font', namespaces=self.processor.namespaces):
                font_name = font_elem.get(f"{{{self.processor.namespaces['w']}}}name")
                if font_name and font_name not in existing_fonts:
                    copied_font = self._deep_copy_xml_element(font_elem)
                    target_fonts.append(copied_font)
                    existing_fonts.add(font_name)
                    added_count += 1
            
            if added_count > 0:
                self.logger.info(f"Font table merge complete, added {added_count} new fonts")
                return etree.tostring(
                    target_fonts,
                    encoding='utf-8',
                    xml_declaration=True,
                    pretty_print=True
                )
            else:
                self.logger.debug("Font table needs no update, all fonts already exist")
                return None
                
        except Exception as e:
            self.logger.warning(f"Failed to merge font tables: {e}")
            # 合并失败时返回源字体表
            return source_font_table
    
    def _assess_template_compatibility(
        self,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure
    ) -> Dict[str, Any]:
        """评估源文档和目标文档的模板兼容性"""
        
        compatibility_info = {
            'is_same_template': False,
            'missing_format_files': [],
            'compatibility_level': 'unknown',
            'recommendations': []
        }
        
        # 检查格式文件的存在性
        format_files = ['styles_xml', 'numbering_xml', 'font_table_xml', 'theme_xml', 'settings_xml']
        
        source_has = {f: getattr(source_structure, f) is not None for f in format_files}
        target_has = {f: getattr(target_structure, f) is not None for f in format_files}
        
        # 计算缺失的格式文件
        for file_attr in format_files:
            if source_has[file_attr] and not target_has[file_attr]:
                compatibility_info['missing_format_files'].append(file_attr)
        
        # 评估兼容性级别
        missing_count = len(compatibility_info['missing_format_files'])
        
        if missing_count == 0:
            if self._compare_template_signatures(source_structure, target_structure):
                compatibility_info['compatibility_level'] = 'same_template'
                compatibility_info['is_same_template'] = True
            else:
                compatibility_info['compatibility_level'] = 'compatible_template'
        elif missing_count <= 2:
            compatibility_info['compatibility_level'] = 'partially_compatible'
            compatibility_info['recommendations'].append('需要复制缺失的格式文件以提高兼容性')
        else:
            compatibility_info['compatibility_level'] = 'low_compatibility'
            compatibility_info['recommendations'].append('目标文档是全新文档或不同模板，需要全面格式同步')
        
        self.logger.info(f"Template compatibility assessment: {compatibility_info['compatibility_level']}")
        if compatibility_info['missing_format_files']:
            self.logger.info(f"Missing format files: {compatibility_info['missing_format_files']}")
        
        return compatibility_info
    
    def _compare_template_signatures(
        self,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure
    ) -> bool:
        """比较文档模板特征以判断是否来自同一模板"""
        
        if not (source_structure.styles_xml and target_structure.styles_xml):
            return False
            
        try:
            source_styles = self.processor.parse_document_xml(source_structure.styles_xml)
            target_styles = self.processor.parse_document_xml(target_structure.styles_xml)
            
            # 获取默认样式的特征
            source_defaults = self._extract_default_styles(source_styles)
            target_defaults = self._extract_default_styles(target_styles)
            
            # 比较核心样式的相似度
            similarity = self._calculate_style_similarity(source_defaults, target_defaults)
            
            return similarity > 0.8  # 80%以上相似度认为是同一模板
            
        except Exception as e:
            self.logger.warning(f"Failed to compare template signatures: {e}")
            return False
    
    def _extract_default_styles(self, styles_root: Element) -> Dict[str, str]:
        """提取默认样式特征"""
        
        defaults = {}
        
        # 查找默认段落和字符样式
        default_styles = styles_root.xpath(
            './/w:style[@w:default="1"]',
            namespaces=self.processor.namespaces
        )
        
        for style in default_styles:
            style_type = style.get(f"{{{self.processor.namespaces['w']}}}type", '')
            
            # 提取字体信息
            fonts = style.xpath('.//w:rFonts', namespaces=self.processor.namespaces)
            if fonts:
                font_elem = fonts[0]
                ascii_font = font_elem.get(f"{{{self.processor.namespaces['w']}}}ascii", '')
                if ascii_font:
                    defaults[f'{style_type}_font'] = ascii_font
            
            # 提取字号信息
            sizes = style.xpath('.//w:sz', namespaces=self.processor.namespaces)
            if sizes:
                size = sizes[0].get(f"{{{self.processor.namespaces['w']}}}val", '')
                if size:
                    defaults[f'{style_type}_size'] = size
        
        return defaults
    
    def _calculate_style_similarity(self, source_defaults: Dict[str, str], target_defaults: Dict[str, str]) -> float:
        """计算样式相似度"""
        
        if not source_defaults or not target_defaults:
            return 0.0
        
        common_keys = set(source_defaults.keys()) & set(target_defaults.keys())
        if not common_keys:
            return 0.0
        
        matches = sum(1 for key in common_keys if source_defaults[key] == target_defaults[key])
        return matches / len(common_keys)
    
    def _enhance_format_compatibility(
        self,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure,
        compatibility_info: Dict[str, Any]
    ) -> None:
        """根据兼容性评估结果增强格式兼容性"""
        
        if compatibility_info['compatibility_level'] == 'same_template':
            self.logger.info("Detected same template, using lightweight sync")
            # 相同模板只需要基本的样式同步
            return
        
        elif compatibility_info['compatibility_level'] in ['low_compatibility', 'partially_compatible']:
            self.logger.info("Detected different template or low compatibility, performing enhanced format sync")
            
            # 执行全面的格式文件同步
            self._sync_format_support_files(source_structure, target_structure)
            
            # 对于低兼容性情况，可能需要更激进的同步策略
            if compatibility_info['compatibility_level'] == 'low_compatibility':
                self.logger.info("Performing comprehensive format file replacement to ensure compatibility")
                # 可以考虑直接替换所有格式文件
                format_attrs = ['font_table_xml', 'theme_xml', 'settings_xml']
                for attr in format_attrs:
                    source_data = getattr(source_structure, attr)
                    if source_data:
                        setattr(target_structure, attr, source_data)
                        self.logger.info(f"Replaced {attr} to improve compatibility")
        
        else:
            self.logger.info("Template compatibility is good, performing standard format sync")
            self._sync_format_support_files(source_structure, target_structure)
    
    def _sync_relationships_and_media(
        self,
        section_info: SectionInfo,
        source_structure: DocumentStructure,
        target_structure: DocumentStructure
    ) -> None:
        """同步关系文件和媒体资源"""
        
        # 这里需要处理document.xml.rels文件
        source_rels_path = 'word/_rels/document.xml.rels'
        
        if source_rels_path not in source_structure.relationships:
            self.logger.warning(f"Source relationships file not found: {source_rels_path}")
            return
        
        # 需要同步的关系ID
        all_rel_ids = section_info.used_relationships | section_info.media_references
        
        if not all_rel_ids:
            self.logger.info("No relationships or media references to sync")
            return
            
        self.logger.info(f"Syncing {len(all_rel_ids)} relationships: {all_rel_ids}")
        
        try:
            # 解析源关系文件
            source_rels_xml = source_structure.relationships[source_rels_path]
            source_rels = self.processor.parse_document_xml(source_rels_xml)
            
            # 解析或创建目标关系文件
            if source_rels_path in target_structure.relationships:
                target_rels_xml = target_structure.relationships[source_rels_path]
                target_rels = self.processor.parse_document_xml(target_rels_xml)
            else:
                # 创建基本关系文件结构
                target_rels = etree.Element(
                    f"{{{self.processor.namespaces['rel']}}}Relationships"
                )
                self.logger.info("Created new target relationships file")
            
            # 添加关系文件命名空间
            rel_namespaces = {
                'rel': 'http://schemas.openxmlformats.org/package/2006/relationships'
            }
            
            # 复制需要的关系和媒体文件
            copied_count = 0
            for rel_id in all_rel_ids:
                # 使用正确的命名空间查询
                source_rel = source_rels.xpath(
                    f".//rel:Relationship[@Id='{rel_id}']",
                    namespaces=rel_namespaces
                )
                
                if source_rel:
                    rel_element = source_rel[0]
                    target_attr = rel_element.get('Target', '')
                    rel_type = rel_element.get('Type', '')
                    
                    self.logger.debug(f"Processing relationship {rel_id}: {rel_type} -> {target_attr}")
                    
                    # 检查目标关系文件中是否已存在
                    existing_rel = target_rels.xpath(
                        f".//rel:Relationship[@Id='{rel_id}']",
                        namespaces=rel_namespaces
                    )
                    
                    if not existing_rel:
                        # 复制关系定义
                        copied_rel = self._deep_copy_xml_element(rel_element)
                        target_rels.append(copied_rel)
                        copied_count += 1
                        
                        # 复制对应的媒体文件
                        if target_attr and ('image' in rel_type.lower() or 'media' in rel_type.lower()):
                            # 处理相对路径
                            if not target_attr.startswith('/'):
                                source_media_path = f"word/{target_attr}"
                            else:
                                source_media_path = target_attr.lstrip('/')
                            
                            if source_media_path in source_structure.media_files:
                                target_structure.media_files[source_media_path] = \
                                    source_structure.media_files[source_media_path]
                                self.logger.debug(f"Copied media file: {source_media_path}")
                            else:
                                self.logger.warning(f"Media file not found in source: {source_media_path}")
                                # 尝试其他可能的路径
                                for media_path in source_structure.media_files:
                                    if target_attr in media_path:
                                        target_structure.media_files[media_path] = \
                                            source_structure.media_files[media_path]
                                        self.logger.debug(f"Found and copied media file: {media_path}")
                                        break
                    else:
                        self.logger.debug(f"Relationship {rel_id} already exists in target")
                else:
                    self.logger.warning(f"Relationship {rel_id} not found in source")
            
            self.logger.info(f"Copied {copied_count} new relationships")
            
            # 更新目标关系文件
            target_structure.relationships[source_rels_path] = etree.tostring(
                target_rels,
                encoding='utf-8',
                xml_declaration=True,
                pretty_print=True
            )
            
        except Exception as e:
            self.logger.error(f"Failed to sync relationships: {e}")
            import traceback
            traceback.print_exc()
    
    def _create_output_document(
        self,
        structure: DocumentStructure,
        document_xml: Element,
        output_path: Union[str, Path]
    ) -> None:
        """创建输出文档"""
        
        output_path = Path(output_path)
        
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as output_zip:
            # 写入主文档XML
            document_xml_bytes = etree.tostring(
                document_xml,
                encoding='utf-8',
                xml_declaration=True,
                pretty_print=True
            )
            output_zip.writestr('word/document.xml', document_xml_bytes)
            
            # 写入格式相关文件
            format_files = {
                'word/styles.xml': structure.styles_xml,
                'word/numbering.xml': structure.numbering_xml,
                'word/fontTable.xml': structure.font_table_xml,
                'word/theme/theme1.xml': structure.theme_xml,
                'word/settings.xml': structure.settings_xml
            }
            
            for file_path, file_data in format_files.items():
                if file_data:
                    output_zip.writestr(file_path, file_data)
                    self.logger.debug(f"Writing format file: {file_path}")
            
            # 写入关系文件
            for rel_path, rel_data in structure.relationships.items():
                output_zip.writestr(rel_path, rel_data)
            
            # 写入媒体文件
            for media_path, media_data in structure.media_files.items():
                output_zip.writestr(media_path, media_data)
            
            # 写入其他文件（排除已处理的格式文件）
            excluded_files = {
                'word/document.xml', 'word/styles.xml', 'word/numbering.xml', 
                'word/fontTable.xml', 'word/theme/theme1.xml', 'word/settings.xml'
            }
            
            for file_path, file_data in structure.other_files.items():
                if file_path not in excluded_files:
                    output_zip.writestr(file_path, file_data)
            
            # 写入内容类型定义
            if structure.content_types:
                # 确保 [Content_Types].xml 中声明了 numbering.xml
                if structure.numbering_xml:
                    try:
                        # 解析已有的 content-types XML
                        ct_root = etree.fromstring(structure.content_types)
                        ns = 'http://schemas.openxmlformats.org/package/2006/content-types'
                        OV = f"{{{ns}}}Override"
                        # 查一下有没有针对 /word/numbering.xml 的 Override
                        numbering_override = ct_root.find(f"{OV}[@PartName='/word/numbering.xml']")
                        if numbering_override is None:
                            # 需要添加 numbering.xml 的声明
                            ov = etree.Element(OV)
                            ov.set('PartName', '/word/numbering.xml')
                            ov.set('ContentType', 'application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml')
                            ct_root.append(ov)
                            # 序列化回字符串
                            structure.content_types = etree.tostring(
                                ct_root,
                                encoding='utf-8',
                                xml_declaration=True,
                                pretty_print=True
                            )
                            self.logger.debug("Added numbering.xml declaration to [Content_Types].xml")
                    except Exception as e:
                        self.logger.warning(f"更新 [Content_Types].xml 时出错：{e}")
                
                output_zip.writestr('[Content_Types].xml', structure.content_types)
    
    def _cleanup_temp_files(self) -> None:
        """清理临时文件"""
        if self._temp_dir and os.path.exists(self._temp_dir):
            shutil.rmtree(self._temp_dir, ignore_errors=True)
            self._temp_dir = None


# 便捷函数
@handle_exceptions(default_return=False)
def copy_section_xml(
    source_path: Union[str, Path],
    target_path: Union[str, Path],
    section_title: str,
    section_level: int = 1,
    output_path: Optional[Union[str, Path]] = None,
    title_pattern: Optional[str] = None
) -> bool:
    """
    便捷函数：使用XML操作复制文档章节，100%保持格式
    
    Args:
        source_path: 源文档路径
        target_path: 目标文档路径
        section_title: 章节标题
        section_level: 章节级别 (1-9)
        output_path: 输出路径，默认覆盖target_path
        title_pattern: 自定义标题匹配模式
        
    Returns:
        bool: 是否成功
        
    Example:
        >>> success = copy_section_xml(
        ...     "source.docx",
        ...     "target.docx",
        ...     "技术架构",
        ...     section_level=1,
        ...     output_path="result.docx"
        ... )
    """
    copier = WordDocumentXMLCopier()
    return copier.copy_section_xml(
        source_path, target_path, section_title, section_level,
        output_path=output_path, title_pattern=title_pattern
    )

if __name__ == "__main__":
    source_path = "temp/技术报告.docx"
    target_path = "temp/target_2.docx"
    section_title = "系统设计方案"
    section_level = 1
    output_path = "temp/output_2.docx"

    copy_section_xml(source_path=source_path, target_path=target_path, section_title=section_title, section_level=section_level, output_path=output_path)
