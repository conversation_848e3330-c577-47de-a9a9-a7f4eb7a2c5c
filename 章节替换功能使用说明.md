# 章节替换功能使用说明

## 功能概述

新增的章节替换功能允许您将源文档中的某一章节内容复制并替换目标文档中的对应章节。这在需要更新文档特定部分内容时非常有用。

## 主要特性

- 支持精确定位要替换的章节（通过标题和级别）
- 保留源章节的所有格式、样式、图片、编号等
- 支持跨文档的章节替换
- 可以指定不同的目标章节标题和级别

## 使用方法

### 1. 基本用法

```python
from word_copy_gpt import replace_section_xml

# 将源文档的"软件设计方案"章节替换目标文档中的同名章节
success = replace_section_xml(
    source_path='源文档.docx',
    target_path='目标文档.docx',
    section_title='软件设计方案',
    section_level=2,  # 二级标题
    output_path='输出文档.docx'
)
```

### 2. 高级用法

```python
# 替换不同名称的章节
success = replace_section_xml(
    source_path='源文档.docx',
    target_path='目标文档.docx',
    section_title='新版软件设计',  # 源文档章节
    section_level=2,
    target_section_title='旧版软件设计',  # 目标文档要被替换的章节
    target_section_level=2,
    output_path='输出文档.docx',
    override_styles=True,  # 覆盖样式
    merge_numbering=True   # 合并编号
)
```

### 3. 在类中使用

```python
from word_copy_gpt import WordDocumentCopier

copier = WordDocumentCopier()
success = copier.replace_section(
    source_path='源文档.docx',
    target_path='目标文档.docx',
    section_title='技术方案',
    section_level=1
)
```

## 参数说明

- `source_path`: 源文档路径
- `target_path`: 目标文档路径
- `section_title`: 源文档中要复制的章节标题
- `section_level`: 源文档中章节级别（1-6）
- `target_section_title`: 目标文档中要被替换的章节标题（可选，默认与源相同）
- `target_section_level`: 目标文档中章节级别（可选，默认与源相同）
- `output_path`: 输出路径（可选，默认覆盖目标文档）
- `override_styles`: 是否覆盖样式（默认True）
- `merge_numbering`: 是否合并编号（默认True）

## 注意事项

1. **文档兼容性**：源文档和目标文档最好来自同一个模板，以确保样式一致性
2. **章节定位**：确保提供的章节标题和级别能够准确定位到目标章节
3. **内容完整性**：替换操作会删除目标章节的所有内容，包括子章节
4. **备份建议**：建议在执行替换前备份目标文档

## 使用示例

参考 `test_replace_section.py` 文件查看完整的使用示例。

## 与 copy_section 的区别

- `copy_section`: 将章节内容追加到目标文档末尾
- `replace_section`: 替换目标文档中的指定章节

选择使用哪个功能取决于您的具体需求。