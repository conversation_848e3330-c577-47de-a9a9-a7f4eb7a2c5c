#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试章节替换功能的示例脚本
"""

from word_copy_gpt import replace_section_xml, parse_document_headings
import os

def test_replace_section():
    """测试章节替换功能"""
    
    # 配置文件路径
    source_doc = 'temp/source.docx'  # 源文档路径
    target_doc = 'temp/target.docx'  # 目标文档路径
    output_doc = 'temp/output_replaced.docx'  # 输出文档路径
    
    # 检查文件是否存在
    if not os.path.exists(source_doc):
        print(f"错误：源文档 {source_doc} 不存在")
        return
    
    if not os.path.exists(target_doc):
        print(f"错误：目标文档 {target_doc} 不存在")
        return
    
    # 先查看源文档的章节结构
    print("=== 源文档章节结构 ===")
    source_headings = parse_document_headings(source_doc, 'flat')
    for heading in source_headings:
        print(f"{'  ' * (heading['level'] - 1)}{heading['level']}级 - {heading['title']} [{heading['style']}]")
    
    print("\n=== 目标文档章节结构 ===")
    target_headings = parse_document_headings(target_doc, 'flat')
    for heading in target_headings:
        print(f"{'  ' * (heading['level'] - 1)}{heading['level']}级 - {heading['title']} [{heading['style']}]")
    
    # 执行章节替换
    print("\n=== 执行章节替换 ===")
    section_title = input("请输入要复制的源文档章节标题: ")
    section_level = int(input("请输入源文档章节级别 (1-6): "))
    
    target_section_title = input("请输入要被替换的目标文档章节标题 (留空则与源相同): ")
    if not target_section_title:
        target_section_title = None
    
    target_section_level = input("请输入目标文档章节级别 (留空则与源相同): ")
    target_section_level = int(target_section_level) if target_section_level else None
    
    # 执行替换
    success = replace_section_xml(
        source_path=source_doc,
        target_path=target_doc,
        section_title=section_title,
        section_level=section_level,
        target_section_title=target_section_title,
        target_section_level=target_section_level,
        output_path=output_doc,
        override_styles=True,
        merge_numbering=True
    )
    
    if success:
        print(f"\n章节替换成功！输出文件: {output_doc}")
        
        # 显示替换后的文档结构
        print("\n=== 替换后文档章节结构 ===")
        output_headings = parse_document_headings(output_doc, 'flat')
        for heading in output_headings:
            print(f"{'  ' * (heading['level'] - 1)}{heading['level']}级 - {heading['title']} [{heading['style']}]")
    else:
        print("\n章节替换失败！请查看日志了解详情。")

if __name__ == '__main__':
    test_replace_section()