"""
SOTA级别的Word文档章节复制工具 - XML操作版本
100%保持原始格式，直接操作DOCX的XML结构
"""

import zipfile
import copy
import logging
from pathlib import Path
from typing import List, Optional, Union, Dict, Set
from dataclasses import dataclass, field

from lxml import etree
from lxml.etree import Element

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WordCopyError(Exception):
    pass

class Config:
    NAMESPACES = {
        'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
        'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
        'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
        'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
        'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture',
        'v': 'urn:schemas-microsoft-com:vml',
        'o': 'urn:schemas-microsoft-com:office:office',
        'w10': 'urn:schemas-microsoft-com:office:word',
        'm': 'http://schemas.openxmlformats.org/officeDocument/2006/math',
        'wp14': 'http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing',
        'wps': 'http://schemas.microsoft.com/office/word/2010/wordprocessingShape',
        'wpg': 'http://schemas.microsoft.com/office/word/2010/wordprocessingGroup',
        'mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
        'rel': 'http://schemas.openxmlformats.org/package/2006/relationships'
    }
    
    HEADING_STYLES_MAP = {
        1: ['Heading1', '标题1'],
        2: ['Heading2', '标题2'], 
        3: ['Heading3', '标题3'],
        4: ['Heading4', '标题4'],
        5: ['Heading5', '标题5'],
        6: ['Heading6', '标题6'],
        7: ['Heading7', '标题7'],
        8: ['Heading8', '标题8'],
        9: ['Heading9', '标题9'],
    }
    
    MEDIA_QUERY_PATTERNS = [
        './/a:blip[@r:embed]',
        './/v:imagedata[@r:id]',
        './/pic:pic//a:blip[@r:embed]',
        './/w:object//o:OLEObject[@r:id]',
        './/w:pict//v:imagedata[@r:id]',
        './/*[@r:embed]',
        './/*[@r:id]'
    ]
    
    FORMAT_FILES_CONFIG = {
        'word/styles.xml': 'styles_xml',
        'word/numbering.xml': 'numbering_xml',
        'word/fontTable.xml': 'font_table_xml', 
        'word/theme/theme1.xml': 'theme_xml',
        'word/settings.xml': 'settings_xml'
    }

@dataclass
class DocumentStructure:
    document_xml: bytes
    styles_xml: Optional[bytes] = None
    numbering_xml: Optional[bytes] = None
    font_table_xml: Optional[bytes] = None
    theme_xml: Optional[bytes] = None
    settings_xml: Optional[bytes] = None
    relationships: Dict[str, bytes] = field(default_factory=dict)
    media_files: Dict[str, bytes] = field(default_factory=dict)
    other_files: Dict[str, bytes] = field(default_factory=dict)
    content_types: Optional[bytes] = None

@dataclass
class SectionInfo:
    xml_elements: List[Element]
    title: str
    level: int
    used_styles: Set[str] = field(default_factory=set)
    used_relationships: Set[str] = field(default_factory=set)
    media_references: Set[str] = field(default_factory=set)
    numbering_references: Set[str] = field(default_factory=set)

class XMLDocumentProcessor:
    def __init__(self):
        self.namespaces = Config.NAMESPACES.copy()
        self._register_namespaces()
    
    def _register_namespaces(self):
        for prefix, uri in self.namespaces.items():
            etree.register_namespace(prefix, uri)
    
    def extract_structure(self, docx_path: Path) -> DocumentStructure:
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            document_xml = docx_zip.read('word/document.xml')
            structure = DocumentStructure(document_xml=document_xml)
            
            for file_path, attr_name in Config.FORMAT_FILES_CONFIG.items():
                try:
                    setattr(structure, attr_name, docx_zip.read(file_path))
                except KeyError:
                    pass
            
            try:
                structure.content_types = docx_zip.read('[Content_Types].xml')
            except KeyError:
                pass
            
            for file_path in docx_zip.namelist():
                if file_path.endswith('.rels'):
                    structure.relationships[file_path] = docx_zip.read(file_path)
                elif file_path.startswith('word/media/'):
                    structure.media_files[file_path] = docx_zip.read(file_path)
                else:
                    structure.other_files[file_path] = docx_zip.read(file_path)
        
        return structure
    
    def parse_xml(self, xml_data: bytes) -> Element:
        parser = etree.XMLParser(recover=True, remove_blank_text=False)
        return etree.fromstring(xml_data, parser)
    
    def find_section(self, document_root: Element, section_title: str, section_level: int) -> Optional[SectionInfo]:
        possible_styles = Config.HEADING_STYLES_MAP.get(section_level, [])
        section_paragraphs = []
        for style_id in possible_styles:
            section_paragraphs.extend(document_root.xpath(f".//w:p[w:pPr/w:pStyle[@w:val='{style_id}']]", namespaces=self.namespaces))
        
        target_paragraph = None
        for para in section_paragraphs:
            text = ''.join(t.text or '' for t in para.xpath('.//w:t', namespaces=self.namespaces)).strip()
            if section_title in text:
                target_paragraph = para
                break
        
        if not target_paragraph:
            return None
        
        body = document_root.find('.//w:body', namespaces=self.namespaces)
        all_elements = list(body)
        start_index = all_elements.index(target_paragraph)
        end_index = len(all_elements)
        for i in range(start_index + 1, len(all_elements)):
            element = all_elements[i]
            if element.tag == f"{{{self.namespaces['w']}}}p" and self._is_section_boundary(element, section_level):
                end_index = i
                break
        
        section_elements = all_elements[start_index:end_index]
        section_info = SectionInfo(
            xml_elements=section_elements,
            title=section_title,
            level=section_level
        )
        self._analyze_dependencies(section_info)
        return section_info
    
    def _is_section_boundary(self, paragraph: Element, current_level: int) -> bool:
        style_val = paragraph.xpath('.//w:pStyle/@w:val', namespaces=self.namespaces)
        if style_val:
            style_val = style_val[0]
            for level, styles in Config.HEADING_STYLES_MAP.items():
                if style_val in styles and level <= current_level:
                    return True
        return False
    
    def _analyze_dependencies(self, section_info: SectionInfo):
        for element in section_info.xml_elements:
            styles = element.xpath('.//w:pStyle | .//w:rStyle | .//w:tblStyle', namespaces=self.namespaces)
            for s in styles:
                val = s.get(f"{{{self.namespaces['w']}}}val")
                if val:
                    section_info.used_styles.add(val)
            
            rels = element.xpath('.//*[@r:id]', namespaces=self.namespaces)
            for r in rels:
                rid = r.get(f"{{{self.namespaces['r']}}}id")
                if rid:
                    section_info.used_relationships.add(rid)
            
            for query in Config.MEDIA_QUERY_PATTERNS:
                media = element.xpath(query, namespaces=self.namespaces)
                for m in media:
                    embed = m.get(f"{{{self.namespaces['r']}}}embed") or m.get(f"{{{self.namespaces['r']}}}id")
                    if embed:
                        section_info.media_references.add(embed)
            
            nums = element.xpath('.//w:numPr/w:numId/@w:val', namespaces=self.namespaces)
            for n in nums:
                section_info.numbering_references.add(n)

class WordDocumentCopier:
    def __init__(self):
        self.processor = XMLDocumentProcessor()
    
    def copy_section(
        self,
        source_path: Union[str, Path],
        target_path: Union[str, Path],
        section_title: str,
        section_level: int = 1,
        output_path: Optional[Union[str, Path]] = None
    ) -> bool:
        source_path = Path(source_path)
        target_path = Path(target_path)
        output_path = Path(output_path or target_path)
        
        source_structure = self.processor.extract_structure(source_path)
        target_structure = self.processor.extract_structure(target_path)
        
        source_doc = self.processor.parse_xml(source_structure.document_xml)
        target_doc = self.processor.parse_xml(target_structure.document_xml)
        
        section_info = self.processor.find_section(source_doc, section_title, section_level)
        if not section_info:
            raise WordCopyError(f"Section '{section_title}' not found")
        
        target_body = target_doc.find('.//w:body', self.processor.namespaces)
        
        for element in section_info.xml_elements:
            copied = copy.deepcopy(element)
            target_body.append(copied)
        
        if section_info.used_styles and source_structure.styles_xml:
            self._sync_styles(section_info.used_styles, source_structure.styles_xml, target_structure)
        
        if section_info.numbering_references and source_structure.numbering_xml:
            self._sync_numbering(section_info.numbering_references, source_structure.numbering_xml, target_structure)
        
        if section_info.used_relationships or section_info.media_references:
            self._sync_rels_and_media(section_info, source_structure, target_structure)
        
        self._sync_support_files(source_structure, target_structure)
        
        self._write_output(target_structure, target_doc, output_path)
        return True
    
    def _sync_styles(self, used_styles: Set[str], source_styles_xml: bytes, target_structure: DocumentStructure):
        if not target_structure.styles_xml:
            target_structure.styles_xml = source_styles_xml
            return
        
        source_styles = self.processor.parse_xml(source_styles_xml)
        target_styles = self.processor.parse_xml(target_structure.styles_xml)
        
        for style_id in used_styles:
            source_style = source_styles.xpath(f".//w:style[@w:styleId='{style_id}']", namespaces=self.processor.namespaces)
            if source_style:
                existing = target_styles.xpath(f".//w:style[@w:styleId='{style_id}']", namespaces=self.processor.namespaces)
                if existing:
                    existing[0].getparent().remove(existing[0])
                target_styles.append(copy.deepcopy(source_style[0]))
        
        target_structure.styles_xml = etree.tostring(target_styles, encoding='utf-8', xml_declaration=True)
    
    def _sync_numbering(self, used_numbering: Set[str], source_numbering_xml: bytes, target_structure: DocumentStructure):
        if not target_structure.numbering_xml:
            target_structure.numbering_xml = source_numbering_xml
            return
        
        source_numbering = self.processor.parse_xml(source_numbering_xml)
        target_numbering = self.processor.parse_xml(target_structure.numbering_xml)
        
        for num_id in used_numbering:
            source_num = source_numbering.xpath(f".//w:num[@w:numId='{num_id}']", namespaces=self.processor.namespaces)
            if source_num:
                abstract_id = source_num[0].xpath('.//w:abstractNumId/@w:val', namespaces=self.processor.namespaces)[0]
                source_abstract = source_numbering.xpath(f".//w:abstractNum[@w:abstractNumId='{abstract_id}']", namespaces=self.processor.namespaces)
                
                existing_num = target_numbering.xpath(f".//w:num[@w:numId='{num_id}']", namespaces=self.processor.namespaces)
                if existing_num:
                    existing_num[0].getparent().remove(existing_num[0])
                target_numbering.append(copy.deepcopy(source_num[0]))
                
                existing_abstract = target_numbering.xpath(f".//w:abstractNum[@w:abstractNumId='{abstract_id}']", namespaces=self.processor.namespaces)
                if existing_abstract:
                    existing_abstract[0].getparent().remove(existing_abstract[0])
                if source_abstract:
                    target_numbering.append(copy.deepcopy(source_abstract[0]))
        
        target_structure.numbering_xml = etree.tostring(target_numbering, encoding='utf-8', xml_declaration=True)
    
    def _sync_rels_and_media(self, section_info: SectionInfo, source_structure: DocumentStructure, target_structure: DocumentStructure):
        rels_path = 'word/_rels/document.xml.rels'
        if rels_path not in source_structure.relationships:
            return
        
        all_rel_ids = section_info.used_relationships | section_info.media_references
        source_rels_xml = source_structure.relationships[rels_path]
        source_rels = self.processor.parse_xml(source_rels_xml)
        
        if rels_path in target_structure.relationships:
            target_rels_xml = target_structure.relationships[rels_path]
            target_rels = self.processor.parse_xml(target_rels_xml)
        else:
            target_rels = etree.Element(f"{{{self.processor.namespaces['rel']}}}Relationships")
        
        rel_ns = {'rel': 'http://schemas.openxmlformats.org/package/2006/relationships'}
        
        for rel_id in all_rel_ids:
            source_rel = source_rels.xpath(f".//rel:Relationship[@Id='{rel_id}']", namespaces=rel_ns)
            if source_rel:
                existing = target_rels.xpath(f".//rel:Relationship[@Id='{rel_id}']", namespaces=rel_ns)
                if not existing:
                    target_rels.append(copy.deepcopy(source_rel[0]))
                    target = source_rel[0].get('Target')
                    if target.startswith('media/'):
                        media_path = f"word/{target}"
                        if media_path in source_structure.media_files:
                            target_structure.media_files[media_path] = source_structure.media_files[media_path]
        
        target_structure.relationships[rels_path] = etree.tostring(target_rels, encoding='utf-8', xml_declaration=True)
    
    def _sync_support_files(self, source_structure: DocumentStructure, target_structure: DocumentStructure):
        attrs = ['font_table_xml', 'theme_xml', 'settings_xml']
        for attr in attrs:
            source_data = getattr(source_structure, attr)
            if source_data and not getattr(target_structure, attr):
                setattr(target_structure, attr, source_data)
    
    def _write_output(self, structure: DocumentStructure, document_xml: Element, output_path: Path):
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as output_zip:
            doc_bytes = etree.tostring(document_xml, encoding='utf-8', xml_declaration=True)
            output_zip.writestr('word/document.xml', doc_bytes)
            
            format_files = {
                'word/styles.xml': structure.styles_xml,
                'word/numbering.xml': structure.numbering_xml,
                'word/fontTable.xml': structure.font_table_xml,
                'word/theme/theme1.xml': structure.theme_xml,
                'word/settings.xml': structure.settings_xml
            }
            for path, data in format_files.items():
                if data:
                    output_zip.writestr(path, data)
            
            for path, data in structure.relationships.items():
                output_zip.writestr(path, data)
            
            for path, data in structure.media_files.items():
                output_zip.writestr(path, data)
            
            for path, data in structure.other_files.items():
                if path not in format_files:
                    output_zip.writestr(path, data)
            
            if structure.content_types:
                output_zip.writestr('[Content_Types].xml', structure.content_types)

def copy_section_xml(
    source_path: Union[str, Path],
    target_path: Union[str, Path],
    section_title: str,
    section_level: int = 1,
    output_path: Optional[Union[str, Path]] = None
) -> bool:
    copier = WordDocumentCopier()
    return copier.copy_section(source_path, target_path, section_title, section_level, output_path)

if __name__ == "__main__":
    source_path = "temp/技术报告.docx"
    target_path = "temp/target_2.docx"
    section_title = "系统设计方案"
    section_level = 1
    output_path = "temp/output_2.docx"

    copy_section_xml(source_path=source_path, target_path=target_path, section_title=section_title, section_level=section_level, output_path=output_path)