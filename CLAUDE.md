# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发环境

### Python 解释器路径
/opt/miniconda3/envs/wordcopy/bin/python

## 开发原则

- 函数设计遵循单一职责
- 注释清晰明了，不宜过多
- 变量名、函数名使用蛇形命名法，类名使用帕斯卡命名法，命名应清晰、无歧义
- 遵循 PEP 8 规范，使用 black 进行代码自动格式化，使用 flake8 进行代码风格检查
- 编写单独的测试文件，而不是在功能代码上编写，测试代码放在 test 目录下
- 测试输出的文件不要放在项目根目录！
- 保持代码清晰整洁，修复 BUG 时，除非必要情况，尽量修改原有代码，而不是添加新的函数（比如"改进版方法"等）
