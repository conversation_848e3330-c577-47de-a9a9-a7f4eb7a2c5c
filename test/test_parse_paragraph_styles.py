# -*- coding: utf-8 -*-
"""
测试 parse_paragraph_styles 函数的单元测试
"""
import unittest
import os
import tempfile
from pathlib import Path
from docx import Document
from docx.shared import Inches

# 添加父目录到路径以导入被测试的模块
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from word_copy_gpt import parse_paragraph_styles


class TestParseParagraphStyles(unittest.TestCase):
    
    def setUp(self):
        """设置测试环境，创建临时测试文档"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_doc_path = os.path.join(self.temp_dir, 'test_document.docx')
        
        # 创建测试文档
        doc = Document()
        
        # 添加标题1
        heading1 = doc.add_heading('测试标题1', level=1)
        
        # 添加普通段落
        paragraph1 = doc.add_paragraph('这是第一个普通段落的内容。')
        
        # 添加标题2
        heading2 = doc.add_heading('测试标题2', level=2)
        
        # 添加另一个普通段落
        paragraph2 = doc.add_paragraph('这是第二个普通段落的内容，包含更多文字。')
        
        # 添加标题3
        heading3 = doc.add_heading('测试标题3', level=3)
        
        # 添加空段落
        empty_paragraph = doc.add_paragraph('')
        
        # 保存文档
        doc.save(self.test_doc_path)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_parse_basic_document(self):
        """测试基本文档解析功能"""
        results = parse_paragraph_styles(self.test_doc_path)
        
        # 检查返回的结果不为空
        self.assertIsNotNone(results)
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        
        # 检查每个结果项的结构
        for item in results:
            self.assertIsInstance(item, dict)
            self.assertIn('content', item)
            self.assertIn('style', item)
            self.assertIsInstance(item['content'], str)
            self.assertIsInstance(item['style'], str)
    
    def test_content_and_styles(self):
        """测试内容和样式的正确性"""
        results = parse_paragraph_styles(self.test_doc_path)
        
        # 检查第一个段落（标题1）
        self.assertEqual(results[0]['content'], '测试标题1')
        self.assertIn('Heading', results[0]['style'])
        
        # 检查第二个段落（普通段落）
        self.assertEqual(results[1]['content'], '这是第一个普通段落的内容。')
        self.assertEqual(results[1]['style'], 'Normal')
        
        # 检查第三个段落（标题2）
        self.assertEqual(results[2]['content'], '测试标题2')
        self.assertIn('Heading', results[2]['style'])
    
    def test_empty_paragraphs(self):
        """测试空段落的处理"""
        results = parse_paragraph_styles(self.test_doc_path)
        
        # 查找空段落
        empty_paragraphs = [item for item in results if item['content'] == '']
        self.assertGreater(len(empty_paragraphs), 0)
        
        # 空段落也应该有样式
        for empty_para in empty_paragraphs:
            self.assertIsNotNone(empty_para['style'])
    
    def test_nonexistent_file(self):
        """测试不存在的文件"""
        nonexistent_path = '/nonexistent/path/to/document.docx'
        results = parse_paragraph_styles(nonexistent_path)
        
        # 应该返回空列表
        self.assertEqual(results, [])
    
    def test_with_path_object(self):
        """测试使用Path对象作为参数"""
        path_obj = Path(self.test_doc_path)
        results = parse_paragraph_styles(path_obj)
        
        # 应该正常工作
        self.assertIsNotNone(results)
        self.assertGreater(len(results), 0)
    
    def test_various_heading_levels(self):
        """测试不同级别的标题"""
        results = parse_paragraph_styles(self.test_doc_path)
        
        # 查找所有标题段落
        headings = [item for item in results if 'Heading' in item['style']]
        self.assertGreaterEqual(len(headings), 3)  # 至少有3个标题
        
        # 检查标题级别
        heading_styles = [item['style'] for item in headings]
        self.assertIn('Heading 1', heading_styles)
        self.assertIn('Heading 2', heading_styles)
        self.assertIn('Heading 3', heading_styles)


class TestParseParagraphStylesWithRealDoc(unittest.TestCase):
    """使用项目中已有的文档进行测试"""
    
    def test_with_existing_document(self):
        """测试使用项目中已有的文档"""
        # 查找项目中可用的.docx文件
        project_root = Path(__file__).parent.parent
        docx_files = list(project_root.glob('**/*.docx'))
        
        if not docx_files:
            self.skipTest("项目中没有找到可用的.docx文件")
        
        # 使用第一个找到的文档进行测试
        test_file = docx_files[0]
        
        # 跳过临时文件和锁定文件
        if test_file.name.startswith('~$'):
            if len(docx_files) > 1:
                test_file = docx_files[1]
            else:
                self.skipTest("只找到临时文件")
        
        try:
            results = parse_paragraph_styles(test_file)
            
            # 基本检查
            self.assertIsInstance(results, list)
            
            if len(results) > 0:
                # 检查结果结构
                for item in results[:5]:  # 只检查前5个
                    self.assertIsInstance(item, dict)
                    self.assertIn('content', item)
                    self.assertIn('style', item)
                
                print(f"\n测试文档: {test_file}")
                print(f"解析到 {len(results)} 个段落")
                print("前5个段落示例:")
                for i, item in enumerate(results[:5]):
                    print(f"  {i+1}. 样式: {item['style']}, 内容: {item['content'][:50]}...")
        
        except Exception as e:
            self.fail(f"解析真实文档失败: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)