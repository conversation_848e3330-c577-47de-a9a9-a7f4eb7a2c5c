# word_copy_xml_refactored_fixed.py
# -*- coding: utf-8 -*-
"""
修复项：
1. 样式可覆盖同步，支持 stylesWithEffects.xml
2. 图片关系去重并重写 rId / o:relid
3. 编号按需合并，避免 numId 冲突导致列表丢失
"""
import re, os, zipfile, shutil, logging, itertools
from pathlib import Path
from typing import List, Optional, Union, Dict, Set
from dataclasses import dataclass, field
from lxml import etree
from lxml.etree import Element
from docx import Document

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

NAMESPACES = {
    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
    'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
    'pkg': 'http://schemas.openxmlformats.org/package/2006/relationships',
    'o':  'urn:schemas-microsoft-com:office:office'
}

for p, u in NAMESPACES.items():
    etree.register_namespace(p, u)

# 全局配置：可匹配的标题样式
DEFAULT_HEADING_STYLES = [
    'Heading 1', 'Heading 2', 'Heading 3', 'Heading 4', 'Heading 5', 'Heading 6',
    '标题 1', '标题 2', '标题 3', '标题 4', '标题 5', '标题 6'
]

@dataclass
class SectionInfo:
    xml_elements: List[Element]
    used_styles: Set[str] = field(default_factory=set)
    used_relationships: Set[str] = field(default_factory=set)
    used_numIds: Set[str] = field(default_factory=set)
    rel_map: Dict[str, str] = field(default_factory=dict)   # src_rId -> dst_rId
    num_map: Dict[str, str] = field(default_factory=dict)   # src_numId -> dst_numId

class WordDocumentCopier:
    def __init__(self):
        self._temp_dir: Optional[str] = None
        self._src_rels: Optional[bytes] = None
        self._src_styles: Optional[bytes] = None
        self._src_styles_fx: Optional[bytes] = None
        self._src_numbering: Optional[bytes] = None
        self._src_media: Dict[str, bytes] = {}

    # ===== 公共入口 =====
    def copy_section(
        self,
        source_path: Union[str, Path],
        target_path: Union[str, Path],
        section_title: str,
        section_level: int = 1,
        output_path: Optional[Union[str, Path]] = None,
        override_styles: bool = True,
        merge_numbering: bool = True
    ) -> bool:
        try:
            output_path = Path(output_path) if output_path else Path(target_path)
            self._extract_source(source_path, section_title, section_level)
            self._prepare_target(target_path)

            # 1 复制正文
            body = self._tgt_doc.xpath('/w:document/w:body', namespaces=NAMESPACES)[0]
            for p in self._sec_info.xml_elements:
                body.append(self._deep_copy_element(p))

            # 2 样式 & effects
            self._sync_styles(override_styles)

            # 3 编号
            if merge_numbering:
                self._sync_numbering()

            # 4 关系 & 媒体
            self._sync_rels_and_media()

            # 5 保存
            self._write_back()
            self._repack(output_path)
            logger.info("复制成功 -> %s", output_path)
            return True
        except Exception as e:
            logger.exception("复制失败: %s", e)
            return False
        finally:
            self._cleanup()

    # ====== 1 读取源文档 ======
    def _extract_source(self, src_path, sec_title, sec_level):
        self._src_path = str(src_path)  # 保存原始文件路径供python-docx使用
        with zipfile.ZipFile(src_path, 'r') as z:
            self._src_doc_xml = etree.fromstring(z.read('word/document.xml'))
            self._src_styles  = self._read_file(z, 'word/styles.xml')
            self._src_styles_fx = self._read_file(z, 'word/stylesWithEffects.xml')
            self._src_numbering = self._read_file(z, 'word/numbering.xml')
            self._src_rels    = self._read_file(z, 'word/_rels/document.xml.rels')
            self._src_media   = self._read_media_files(z)
        self._sec_info = self._find_section(self._src_doc_xml, sec_title, sec_level)
        if not self._sec_info:
            raise RuntimeError(f"未找到章节『{sec_title}』")

    # ====== 2 解压目标文档 ======
    def _prepare_target(self, tgt_path):
        self._temp_dir = f"_tmp_{os.getpid()}"
        os.makedirs(self._temp_dir, exist_ok=True)
        with zipfile.ZipFile(tgt_path, 'r') as z:
            z.extractall(self._temp_dir)
        # 持久化对象句柄
        self._tgt_doc_path = os.path.join(self._temp_dir, 'word/document.xml')
        self._tgt_doc = etree.parse(self._tgt_doc_path).getroot()

    # ----------------------------------------------------------------------
    # 核心步骤
    # ----------------------------------------------------------------------
    def _sync_styles(self, override: bool):
        if not self._sec_info.used_styles or not self._src_styles:
            return
        tgt_styles_path = os.path.join(self._temp_dir, 'word/styles.xml')
        tgt_root = etree.parse(tgt_styles_path).getroot() if os.path.exists(tgt_styles_path) \
            else etree.Element(f"{{{NAMESPACES['w']}}}styles")

        src_root = etree.fromstring(self._src_styles)
        copied_styles = set()  # 防止循环引用
        
        def copy_style(style_id):
            if not style_id or style_id in copied_styles: 
                return
            copied_styles.add(style_id)
            
            exist = tgt_root.find(f".//w:style[@w:styleId='{style_id}']", NAMESPACES)
            src_elem = src_root.find(f".//w:style[@w:styleId='{style_id}']", NAMESPACES)
            if src_elem is None: 
                return
            if exist is not None and override:
                tgt_root.remove(exist)
            if exist is None or override:
                tgt_root.append(self._deep_copy_element(src_elem))
                # 递归复制 basedOn / link
                for ref in src_elem.xpath('./w:basedOn/@w:val | ./w:link/@w:val', namespaces=NAMESPACES):
                    copy_style(ref)
        for sid in self._sec_info.used_styles:
            copy_style(sid)
        etree.ElementTree(tgt_root).write(tgt_styles_path, encoding='UTF-8', xml_declaration=True)

        # 同步 stylesWithEffects（如有）
        if self._src_styles_fx:
            tgt_fx = os.path.join(self._temp_dir, 'word/stylesWithEffects.xml')
            if not os.path.exists(tgt_fx) or override:
                with open(tgt_fx, 'wb') as f:
                    f.write(self._src_styles_fx)

    # ---- 编号合并 ----
    def _sync_numbering(self):
        if not self._src_numbering or not self._sec_info.used_numIds:
            return

        # ---------- 1. 复制 / 合并 numbering.xml ----------
        tgt_num_path = os.path.join(self._temp_dir, 'word/numbering.xml')
        tgt_root = etree.parse(tgt_num_path).getroot() if os.path.exists(tgt_num_path) \
            else etree.Element(f"{{{NAMESPACES['w']}}}numbering")
        src_root = etree.fromstring(self._src_numbering)

        existing_num_ids = {int(n.get(f"{{{NAMESPACES['w']}}}numId"))
                            for n in tgt_root.findall('.//w:num', NAMESPACES)} or {0}
        existing_abs_ids = {int(a.get(f"{{{NAMESPACES['w']}}}abstractNumId"))
                            for a in tgt_root.findall('.//w:abstractNum', NAMESPACES)} or {0}
        next_num_id, next_abs_id = max(existing_num_ids) + 1, max(existing_abs_ids) + 1

        src_nums = {n.get(f"{{{NAMESPACES['w']}}}numId"): n
                    for n in src_root.findall('.//w:num', NAMESPACES)}
        src_abs  = {a.get(f"{{{NAMESPACES['w']}}}abstractNumId"): a
                    for a in src_root.findall('.//w:abstractNum', NAMESPACES)}

        abs_id_map: Dict[str, str] = {}
        for numId in self._sec_info.used_numIds:
            src_num = src_nums.get(numId)
            if src_num is None:
                continue

            absId = src_num.find('./w:abstractNumId', NAMESPACES).get(f"{{{NAMESPACES['w']}}}val")
            abs_elem = src_abs.get(absId)
            if abs_elem is None:
                continue

            # ------- abstractNum -------
            new_abs_id = absId
            if tgt_root.find(f".//w:abstractNum[@w:abstractNumId='{absId}']", NAMESPACES) is not None:
                new_abs_id = str(next_abs_id); next_abs_id += 1
                abs_id_map[absId] = new_abs_id

            if new_abs_id not in {a.get(f"{{{NAMESPACES['w']}}}abstractNumId")
                                  for a in tgt_root.findall('.//w:abstractNum', NAMESPACES)}:
                abs_copy = self._deep_copy_element(abs_elem)
                abs_copy.set(f"{{{NAMESPACES['w']}}}abstractNumId", new_abs_id)
                tgt_root.append(abs_copy)

            # ------- num -------
            new_num_id = numId
            if tgt_root.find(f".//w:num[@w:numId='{numId}']", NAMESPACES) is not None:
                new_num_id = str(next_num_id); next_num_id += 1
                self._sec_info.num_map[numId] = new_num_id

            num_copy = self._deep_copy_element(src_num)
            num_copy.set(f"{{{NAMESPACES['w']}}}numId", new_num_id)
            num_copy.find('./w:abstractNumId', NAMESPACES).set(
                f"{{{NAMESPACES['w']}}}val", abs_id_map.get(absId, absId))
            tgt_root.append(num_copy)

        etree.ElementTree(tgt_root).write(tgt_num_path, encoding='UTF-8', xml_declaration=True)

        # ---------- 2. 更新文档段落中的 numId ----------
        for p in self._tgt_doc.xpath('.//w:p', namespaces=NAMESPACES)[-len(self._sec_info.xml_elements):]:
            numId_elem = p.find('.//w:numPr/w:numId', NAMESPACES)
            if numId_elem is not None:
                old_id = numId_elem.get(f"{{{NAMESPACES['w']}}}val")
                if old_id in self._sec_info.num_map:
                    numId_elem.set(f"{{{NAMESPACES['w']}}}val", self._sec_info.num_map[old_id])

        # ---------- 3. 补关系 (rels) ----------
        rels_path = os.path.join(self._temp_dir, 'word/_rels/document.xml.rels')
        rels_root = etree.parse(rels_path).getroot() if os.path.exists(rels_path) \
            else etree.Element('Relationships', nsmap={'': NAMESPACES['pkg']})

        rel_type = 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering'
        if rels_root.find(f"pkg:Relationship[@Type='{rel_type}']",
                          namespaces={'pkg': NAMESPACES['pkg']}) is None:
            existing_ids = {r.get('Id') for r in rels_root}
            idx = 1
            while f"rId{idx}" in existing_ids:
                idx += 1
            rel = etree.SubElement(rels_root, 'Relationship')
            rel.set('Id', f"rId{idx}")
            rel.set('Type', rel_type)
            rel.set('Target', 'numbering.xml')
            etree.ElementTree(rels_root).write(rels_path, encoding='UTF-8', xml_declaration=True)

        # ---------- 4. 补 Content_Types ----------
        ct_path = os.path.join(self._temp_dir, '[Content_Types].xml')
        ct_root = etree.parse(ct_path).getroot()
        ns_ct = {'ct': 'http://schemas.openxmlformats.org/package/2006/content-types'}
        if ct_root.find("./ct:Override[@PartName='/word/numbering.xml']", namespaces=ns_ct) is None:
            override = etree.SubElement(ct_root, 'Override')
            override.set('PartName', '/word/numbering.xml')
            override.set('ContentType',
                         'application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml')
            etree.ElementTree(ct_root).write(ct_path, encoding='UTF-8', xml_declaration=True)
        

    # ---- 关系 & 媒体 ----
    def _sync_rels_and_media(self):
        if not (self._sec_info.used_relationships and self._src_rels):
            return
        rels_path = os.path.join(self._temp_dir, 'word/_rels/document.xml.rels')
        tgt_root = etree.parse(rels_path).getroot() if os.path.exists(rels_path) \
            else etree.Element('Relationships', nsmap={'': NAMESPACES['pkg']})
        src_root = etree.fromstring(self._src_rels)

        existing_rids = {rel.get('Id') for rel in tgt_root.findall('pkg:Relationship', namespaces={'pkg': NAMESPACES['pkg']})}

        # 复制图片
        for rel in src_root.findall('pkg:Relationship', namespaces={'pkg': NAMESPACES['pkg']}):
            rid = rel.get('Id')
            if rid not in self._sec_info.used_relationships:
                continue
            new_rid = rid
            # 如果已存在，生成新 Id
            if new_rid in existing_rids:
                for i in itertools.count(1):
                    cand = f"{rid}_{i}"
                    if cand not in existing_rids:
                        new_rid = cand; break
            # 写关系
            rel_copy = self._deep_copy_element(rel)
            rel_copy.set('Id', new_rid)
            tgt_root.append(rel_copy)
            existing_rids.add(new_rid)
            self._sec_info.rel_map[rid] = new_rid

            # 复制媒体文件（如有）
            target = rel.get('Target')
            if target and target.startswith('media/'):
                src_bin = self._src_media.get(f'word/{target}')
                if src_bin:
                    dst_media_path = os.path.join(self._temp_dir, 'word', target)
                    if os.path.exists(dst_media_path):  # 重命名避免覆盖
                        base, ext = os.path.splitext(target)
                        for i in itertools.count(1):
                            new_target = f"{base}_{i}{ext}"
                            dst_media_path = os.path.join(self._temp_dir, 'word', new_target)
                            if not os.path.exists(dst_media_path):
                                rel_copy.set('Target', new_target)
                                break
                    os.makedirs(os.path.dirname(dst_media_path), exist_ok=True)
                    with open(dst_media_path, 'wb') as f:
                        f.write(src_bin)

        etree.ElementTree(tgt_root).write(rels_path, encoding='UTF-8', xml_declaration=True)

        # 回写段落中的 rId / o:relid
        for p in self._tgt_doc.xpath('.//w:drawing | .//v:imagedata | .//o:OLEObject', namespaces={**NAMESPACES, 'v': 'urn:schemas-microsoft-com:vml', 'o': NAMESPACES['o']}):
            for attr in ('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed',
                         '{http://schemas.openxmlformats.org/officeDocument/2006/relationships}link',
                         '{http://schemas.openxmlformats.org/officeDocument/2006/relationships}id',
                         '{urn:schemas-microsoft-com:office:office}relid'):
                val = p.get(attr)
                if val and val in self._sec_info.rel_map:
                    p.set(attr, self._sec_info.rel_map[val])

    # ----------------------------------------------------------------------
    # 工具函数
    # ----------------------------------------------------------------------
    def _find_section(self, root: Element, title: str, level: int) -> SectionInfo:
        logger.info("开始查找章节: %s (级别: %s)", title, level)
        
        try:
            # 使用python-docx打开原始文档进行样式识别
            doc = Document(self._src_path)
            
            # 构建目标样式列表（基于级别和全局配置）
            target_styles = []
            for style_template in DEFAULT_HEADING_STYLES:
                if f' {level}' in style_template:
                    target_styles.append(style_template)
            
            logger.info("目标样式: %s", target_styles)
            
            # 查找匹配的标题段落文本
            matched_title_text = None
            matched_style = None
            pattern = re.compile(re.escape(title), re.I)
            
            for para in doc.paragraphs:
                para_style = para.style.name if para.style else ''
                
                # 优先匹配指定级别的标题样式
                if para_style in target_styles:
                    if pattern.search(para.text):
                        matched_title_text = para.text.strip()
                        matched_style = para_style
                        logger.info("找到精确匹配: '%s' [样式: %s]", matched_title_text[:50], matched_style)
                        break
                
                # 如果没有找到精确样式匹配，尝试其他标题样式
                elif any(heading in para_style for heading in ['Heading', '标题']) and pattern.search(para.text):
                    matched_title_text = para.text.strip()
                    matched_style = para_style
                    logger.info("找到候选匹配: '%s' [样式: %s]", matched_title_text[:50], matched_style)
                    break
            
            if matched_title_text is None:
                logger.error("python-docx未找到匹配标题，回退到XML处理")
                return self._find_section_fallback(root, title, level)
            
            # 在XML中根据文本内容查找对应的段落元素
            return self._find_section_by_text_and_style(root, matched_title_text, matched_style, level)
            
        except Exception as e:
            logger.error("python-docx处理失败，回退到原始实现: %s", e)
            return self._find_section_fallback(root, title, level)

    def _find_section_by_text_and_style(self, root: Element, title_text: str, title_style: str, level: int) -> SectionInfo:
        """根据python-docx识别的文本和样式在XML中查找章节"""
        logger.info("在XML中查找标题文本: '%s'", title_text[:50])
        
        # 在XML中查找匹配文本的段落
        all_paras = root.xpath('.//w:p', namespaces=NAMESPACES)
        start_elem = None
        
        # 精确文本匹配
        text_pattern = re.compile(re.escape(title_text.strip()), re.I)
        
        for p in all_paras:
            para_text = ''.join(t.text or '' for t in p.xpath('.//w:t', namespaces=NAMESPACES)).strip()
            if text_pattern.search(para_text):
                start_elem = p
                logger.info("在XML中找到匹配段落: '%s'", para_text[:50])
                break
        
        if start_elem is None:
            # 如果精确匹配失败，尝试部分匹配
            title_keywords = title_text.strip().split()
            for p in all_paras:
                para_text = ''.join(t.text or '' for t in p.xpath('.//w:t', namespaces=NAMESPACES)).strip()
                if all(keyword in para_text for keyword in title_keywords):
                    start_elem = p
                    logger.info("在XML中找到部分匹配段落: '%s'", para_text[:50])
                    break
        
        if start_elem is None:
            logger.error("在XML中未找到匹配的标题段落")
            return None
        
        # 收集章节内容 - 更准确的方法
        section_elements = []
        
        # 构建终止样式列表
        terminating_styles = set()
        for style_template in DEFAULT_HEADING_STYLES:
            for stop_level in range(1, level + 1):
                if f' {stop_level}' in style_template:
                    terminating_styles.add(style_template.replace(' ', ''))
        
        logger.info("终止样式: %s", terminating_styles)
        
        # 获取文档体中的所有直接子元素（段落、表格等）
        body = root.xpath('/w:document/w:body', namespaces=NAMESPACES)[0]
        all_body_elements = body.xpath('./w:p | ./w:tbl | ./w:sectPr', namespaces=NAMESPACES)
        
        # 找到起始元素的位置
        start_index = None
        for i, elem in enumerate(all_body_elements):
            if elem == start_elem:
                start_index = i
                break
        
        if start_index is None:
            logger.error("无法在文档体中找到起始元素")
            return None
        
        # 从起始元素开始收集，直到遇到终止条件
        for i in range(start_index, len(all_body_elements)):
            current_elem = all_body_elements[i]
            
            # 如果不是起始元素，检查是否为终止条件
            if i > start_index and current_elem.tag == f"{{{NAMESPACES['w']}}}p":
                style_elem = current_elem.find('.//w:pStyle', NAMESPACES)
                if style_elem is not None:
                    style_val = style_elem.get(f"{{{NAMESPACES['w']}}}val")
                    if style_val in terminating_styles:
                        logger.info("遇到终止样式: %s", style_val)
                        break
            
            section_elements.append(current_elem)
        
        logger.info("收集章节元素: %d 个", len(section_elements))
        
        # 创建SectionInfo并分析依赖
        info = SectionInfo(section_elements)
        self._analyze_dependencies(info)
        return info


    def _find_section_fallback(self, root: Element, title: str, level: int) -> SectionInfo:
        """原始实现的备用版本"""
        logger.info("使用备用查找策略")
        
        # 多种策略查找标题段落
        candidates = []
        
        # 策略1: 标准标题样式匹配 (Heading1, 标题1等)
        for style_template in DEFAULT_HEADING_STYLES:
            if f' {level}' in style_template:
                style_name = style_template.replace(' ', '')
                xpath = f".//w:p[w:pPr/w:pStyle[@w:val='{style_name}']]"
                matches = root.xpath(xpath, namespaces=NAMESPACES)
                candidates.extend(matches)
        
        # 策略2: 文本内容直接匹配 (不依赖样式级别)
        pattern = re.compile(re.escape(title), re.I)
        all_paras = root.xpath(".//w:p[w:pPr/w:pStyle]", namespaces=NAMESPACES)
        
        # 在所有有样式的段落中搜索匹配的文本
        for p in all_paras:
            text_content = ''.join(t.text or '' for t in p.xpath('.//w:t', namespaces=NAMESPACES))
            if pattern.search(text_content):
                candidates.append(p)
        
        # 去重
        candidates = list({id(p): p for p in candidates}.values())
        logger.info("备用策略找到 %d 个候选段落", len(candidates))
        
        # 在候选段落中查找文本匹配
        start = None
        for p in candidates:
            text_content = ''.join(t.text or '' for t in p.xpath('.//w:t', namespaces=NAMESPACES))
            if pattern.search(text_content):
                start = p
                logger.info("备用策略找到匹配段落: '%s'", text_content.strip()[:50])
                break
        
        if start is None:
            logger.error("备用策略也未找到匹配标题 '%s' 的段落", title)
            return None
            
        # 收集章节内容
        elems, cur = [], start
        while cur is not None:
            elems.append(cur)
            nxt = cur.getnext()
            if nxt is None:
                break
            pstyle = nxt.find('.//w:pStyle', NAMESPACES)
            if pstyle is not None:
                val = pstyle.get(f"{{{NAMESPACES['w']}}}val")
                # 同级或更高级标题终止
                if val:
                    for style_template in DEFAULT_HEADING_STYLES:
                        for stop_level in range(1, level + 1):
                            if f' {stop_level}' in style_template and style_template.replace(' ', '') == val:
                                logger.info("备用策略遇到终止样式: %s", val)
                                cur = None
                                break
                        if cur is None:
                            break
                    if cur is None:
                        break
            cur = nxt
            
        info = SectionInfo(elems)
        self._analyze_dependencies(info)
        return info

    def _analyze_dependencies(self, info: SectionInfo):
        for elem in info.xml_elements:
            # 段落和运行样式
            info.used_styles.update(elem.xpath('.//@w:val[parent::w:pStyle or parent::w:rStyle]', namespaces=NAMESPACES))
            
            # 表格样式 - 这是表格格式丢失的关键修复
            info.used_styles.update(elem.xpath('.//@w:val[parent::w:tblStyle]', namespaces=NAMESPACES))
            info.used_styles.update(elem.xpath('.//w:tbl//w:tblPr//w:tblStyle/@w:val', namespaces=NAMESPACES))
            
            # 表格单元格样式
            info.used_styles.update(elem.xpath('.//w:tc//w:tcPr//w:tcStyle/@w:val', namespaces=NAMESPACES))
            
            # 条件格式样式 (表格条纹等)
            info.used_styles.update(elem.xpath('.//w:tblLook/@w:val', namespaces=NAMESPACES))
            
            # 所有其他w:val属性样式
            info.used_styles.update(elem.xpath('.//@w:val[contains(local-name(parent::*), "Style")]', namespaces=NAMESPACES))
            
            # 关系和媒体
            info.used_relationships.update(elem.xpath('.//@r:embed | .//@r:link | .//@r:id', namespaces=NAMESPACES))
            info.used_relationships.update(elem.xpath('.//@o:relid', namespaces={'o': NAMESPACES['o']}))
            
            # 编号
            info.used_numIds.update(elem.xpath('.//w:numPr/w:numId/@w:val', namespaces=NAMESPACES))

    # ---------- 通用 ----------
    def _read_file(self, z: zipfile.ZipFile, path: str) -> Optional[bytes]:
        try: return z.read(path)
        except KeyError: return None

    def _read_media_files(self, z: zipfile.ZipFile) -> Dict[str, bytes]:
        return {name: z.read(name) for name in z.namelist() if name.startswith('word/media/') and not name.endswith('/')}

    def _deep_copy_element(self, e: Element) -> Element:
        return etree.fromstring(etree.tostring(e))

    # ---------- 写回 & 打包 ----------
    def _write_back(self):
        etree.ElementTree(self._tgt_doc).write(self._tgt_doc_path, encoding='UTF-8', xml_declaration=True)

    def _repack(self, out_path: Path):
        with zipfile.ZipFile(out_path, 'w', zipfile.ZIP_DEFLATED) as z:
            for root, _, files in os.walk(self._temp_dir):
                for f in files:
                    full = os.path.join(root, f)
                    z.write(full, os.path.relpath(full, self._temp_dir))

    def _cleanup(self):
        if self._temp_dir and os.path.isdir(self._temp_dir):
            shutil.rmtree(self._temp_dir, ignore_errors=True)

# ----------------------------------------------------------------------
# 便捷调用
# ----------------------------------------------------------------------
def copy_section_xml(source_path, target_path, section_title, section_level=1,
                     output_path=None, override_styles=True, merge_numbering=True):
    return WordDocumentCopier().copy_section(
        source_path, target_path, section_title, section_level,
        output_path, override_styles, merge_numbering
    )

if __name__ == '__main__':
    # ok = copy_section_xml('temp/技术报告.docx', 'temp/target_2.docx', '系统设计方案', output_path='out.docx')
    ok = copy_section_xml('temp/ZD大模型技术研究任务书.docx', 'temp/target_2.docx', '保密要求', output_path='out.docx')
    print("复制成功" if ok else "复制失败")